name: Deploy to Server

on:
  push:
    branches:
      - main
  workflow_dispatch:
    inputs:
      branch:
        description: 'Branch to deploy'
        required: true
        default: 'dev'

jobs:
  deploy:
    runs-on: ubuntu-latest
    steps:
      - name: Checkout repository
        uses: actions/checkout@v4
        with:
          ref: ${{ github.event.inputs.branch }}

      - name: Rsync files to server
        uses: burnett01/rsync-deployments@7.0.2
        with:
          switches: -acvzH --partial --delete --exclude=".venv" --exclude="__pycache__" --exclude="logs" --exclude="sitemap"
          path: src/
          remote_path: /opt/albumd/
          remote_host: ${{ secrets.SSH_HOST }}
          remote_port: ${{ secrets.SSH_PORT }}
          remote_user: ${{ secrets.SSH_USER }}
          remote_key: ${{ secrets.SSH_PRIVATE_KEY }}

      - name: Execute remote commands
        uses: appleboy/ssh-action@v1.2.2
        with:
          host: ${{ secrets.SSH_HOST }}
          port: ${{ secrets.SSH_PORT }}
          username: ${{ secrets.SSH_USER }}
          key: ${{ secrets.SSH_PRIVATE_KEY }}
          script: |
            source /opt/albumd/.venv/bin/activate
            uv sync --project /opt/albumd
            sudo systemctl daemon-reload
            sudo systemctl reload albumd.service
            sudo systemctl reload nginx.service
