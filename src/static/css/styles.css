/* 全局基础样式 */
* {
  -webkit-box-sizing: border-box;
  -moz-box-sizing: border-box;
  box-sizing: border-box;
}
*:hover,
*:focus {
  transition: all 0.1s ease-in-out;
}
:root {
  /* Basic Colors */
  --red: #fb2d2d;
  --rose: #ff00af;
  --pink: #fb7299;
  --lightpink: #ee91d0;
  --orange: #ff6a00;
  --yellow: #fff700;
  --golden: #fb0;
  --darkgolden: #866127;
  --green: #00ee00;
  --lightgreen: #a1d013;
  --wxgreen: #65d979;
  --cyan: #2bc0c0;
  --blue: #0096fe;
  --skyblue: #268df7;
  --lightblue: #00caff;
  --purple: mediumorchid;
  --lightpurple: #b06bed;
  --dustypurple: mediumpurple;
  --brown: #c07c39;
  --milk: #fffdf0;
  --darkgray: #444;
  /* Theme Colors */
  --fg-primary: #373737;
  --fg-emp: #02386d;
  --bg-primary: white;
  --bg-secondary: #fcfcfc;
  --bg-tertiary: #eee;
  --bg-img: white;
  --bg-placeholder: #444;
  --border-shadow: rgba(0, 0, 0, 0.35);
}

/* 根据系统自动切换 */
@media (prefers-color-scheme: dark) {
  :root {
    --fg-primary: #ccc;
    --fg-emp: #0087e7;
    --bg-primary: #151515;
    --bg-secondary: #222;
    --bg-tertiary: #333;
    --bg-img: #333;
    --bg-placeholder: #444;
    --border-shadow: rgba(255, 255, 255, 0.3);
  }
}

html,
body {
  height: 100%;
  margin: 0;
  padding: 0;
}
html {
  scroll-behavior: smooth;
}

body {
  color: var(--fg-primary);
  background-color: var(--bg-primary);
  display: flex;
  flex-direction: column;
  font-size: 16px;
  line-height: 1.4em;
  min-height: 100vh;
}

a {
  color: var(--blue);
  text-decoration: none;
}
a:hover,
a:focus,
a h3:hover,
a h3:focus,
a h4:hover,
a h4:focus,
a h5:hover,
a h5:focus,
a h6:hover,
a h6:focus {
  color: var(--pink);
}

h1,
h2,
h3,
h4,
h5,
h6,
label {
  color: var(--fg-emp);
  line-height: 1.6em;
}
h5,
h6 {
  margin-bottom: 0.2em;
}

hr {
  border: 0;
  border-top: 0.1px solid #ccc;
  margin: 1em 0;
  width: 100%;
}
p {
  margin: 0.75em 0;
}

input[type='text'],
input[type='password'],
input[type='email'],
input[type='search'] {
  color: var(--fg-primary);
  padding: 6px 10px !important;
  background-color: var(--bg-primary) !important;
  box-shadow: 0 0 1px inset var(--border-shadow) !important;
  border: 1px solid var(--border-shadow) !important;
}

[id] {
  scroll-margin-top: 2.1em;
}

/* 页面布局 */
header {
  background-color: var(--bg-secondary);
  padding-bottom: 2.3em;
  width: 100%;
  transition: transform 0.2s ease;
}

main {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0;
  flex: 1 0 auto;
}

footer {
  max-width: 1200px;
  width: 100%;
  margin: 0 auto;
  font-size: 0.8em;
  text-align: center;
  flex-shrink: 0;
  color: gray;
}

/* 前景色 */
.fg-red {
  color: var(--red);
}
.bg-red {
  background-color: var(--red);
}
.fg-rose {
  color: var(--rose);
}
.bg-rose {
  background-color: var(--rose);
}
.fg-pink {
  color: var(--pink);
}
.bg-pink {
  background-color: var(--pink);
}
.fg-lightpink {
  color: var(--lightpink);
}
.bg-lightpink {
  background-color: var(--lightpink);
}
.fg-orange {
  color: var(--orange);
}
.bg-orange {
  background-color: var(--orange);
}
.fg-yellow {
  color: var(--yellow);
}
.bg-yellow {
  background-color: var(--yellow);
}
.fg-golden {
  color: var(--golden);
}
.bg-golden {
  background-color: var(--golden);
}
.fg-green {
  color: var(--green);
}
.fg-lightgreen {
  color: var(--lightgreen);
}
.bg-lightgreen {
  background-color: var(--lightgreen);
}
.fg-wxgreen {
  color: var(--wxgreen);
}
.fg-cyan {
  color: var(--cyan);
}
.bg-cyan {
  background-color: var(--cyan);
}
.fg-blue {
  color: var(--blue);
}
.bg-blue {
  background-color: var(--blue);
}
.fg-lightblue {
  color: var(--lightblue);
}
.bg-lightblue {
  background-color: var(--lightblue);
}
.fg-skyblue {
  color: var(--skyblue);
}
.bg-skyblue {
  background-color: var(--skyblue);
}
.fg-emp {
  color: var(--fg-emp);
}
.fg-purple {
  color: var(--purple);
}
.bg-purple {
  background-color: var(--purple);
}
.fg-dustypurple {
  color: var(--dustypurple);
}
.bg-dustypurple {
  background-color: var(--dustypurple);
}
.fg-brown {
  color: var(--brown);
}
.bg-brown {
  background-color: var(--brown);
}
.fg-white {
  color: white;
}
.bg-white {
  background-color: white;
}
.fg-gray {
  color: gray;
}
.fg-lightgray {
  color: lightgray;
}
.fg-darkgray {
  color: var(--darkgray);
}
.fg-black {
  color: black;
}
.bg-black {
  background-color: black;
}
.bg-dazzling {
  background: linear-gradient(45deg, var(--blue), var(--lightpurple), var(--rose));
}
.bg-dazzling:hover,
.bg-dazzling:focus {
  color: white;
  background: linear-gradient(225deg, var(--blue), var(--lightpurple), var(--rose));
}
.gbg-red {
  color: white;
  background: linear-gradient(135deg, var(--red) 10%, #fd7a64);
  background-color: var(--red);
}
.gbg-golden {
  color: var(--darkgolden);
  background: linear-gradient(25deg, #eabe7b, #f5e3c7 30% 50%, #edc788);
  background-color: #edc788;
}
.gbg-yellow {
  background: linear-gradient(135deg, #f59f54 10%, #ff6922);
  background-color: #ff6922;
}
.gbg-green {
  background: linear-gradient(135deg, #60e464 10%, #5cb85b);
  background-color: #5cb85b;
}
.gbg-cyan {
  background: linear-gradient(140deg, #039ab3 10%, #58dbcf 90%);
  background-color: #58dbcf;
}
.gbg-blue {
  color: white;
  background: linear-gradient(135deg, var(--blue) 10%, #59c3fb);
  background-color: var(--skyblue);
}
.gbg-pink {
  color: white;
  background: linear-gradient(135deg, var(--pink) 30%, #ff5e7f);
  background-color: var(--pink);
}
.gbg-rose {
  color: white;
  background: linear-gradient(135deg, var(--rose) 30%, #ff88d9);
  background-color: var(--rose);
}
.gbg-purple {
  background: linear-gradient(135deg, #f98dfb 10%, #ea00f9);
  background-color: #ea00f9;
}
.gbg-black {
  color: white;
  background: linear-gradient(317deg, #4d4c4c 30%, #7b7b7b 70%, #5f5c5c);
  background-color: #5f5c5c;
}

/* 强调色 */
.emp-white {
  color: white;
  background-color: #ffffff30;
  padding: 0 5px;
  border-radius: 5px;
  display: inline-block;
}
.emp-pink {
  color: var(--rose);
  background-color: #ee91d030;
  padding: 0 5px;
  border-radius: 5px;
  display: inline-block;
}
.emp-blue {
  color: var(--blue);
  background-color: #2bc0c030;
  padding: 0 5px;
  border-radius: 5px;
  display: inline-block;
}
.emp-dark {
  color: #34495e;
  background-color: #60606030;
  padding: 0 5px;
  border-radius: 5px;
  display: inline-block;
}
.emp-del {
  color: gray;
  padding: 0 5px;
  text-decoration: line-through;
  text-decoration-thickness: 1px;
  display: inline-block;
}
.emp-del::before,
.emp-del::after {
  content: '\00a0\00a0';
  text-decoration: inherit;
}

/* 大小 */
.xs {
  font-size: 0.6em;
}
.sm {
  font-size: 0.8em;
}
.md {
  font-size: 1em;
}
.lg {
  font-size: 1.2em;
}
.xl {
  font-size: 1.5em;
}
.xxl {
  font-size: 1.8em;
}
.mono {
  font-family: monospace;
}
.mono-3 {
  font-family: monospace;
  width: 6ch;
  display: inline-block;
  text-align: right;
}
.b-100 {
  font-weight: 100;
}
.b-300 {
  font-weight: 300;
}
.b-500 {
  font-weight: 500;
}
.b-700 {
  font-weight: 700;
}
.b-900 {
  font-weight: 900;
}
.line-2 {
  line-height: 2em;
}
/* Padding */
.pd-0 {
  padding: 0;
}
.pd-1-5 {
  padding: 0.2em;
}
.pd-2-5 {
  padding: 0.4em;
}
.pd-3-5 {
  padding: 0.6em;
}
.pd-4-5 {
  padding: 0.8em;
}
.pd-1 {
  padding: 1em;
}
.pd-2 {
  padding: 2em;
}
.pd-5 {
  padding: 5em;
}
.pdl-1 {
  padding-left: 1em;
}
.pdh-1 {
  padding: 0.5em 1em;
}
.pdh-2 {
  padding: 1em 2em;
}
.pdh-3 {
  padding: 1.5em 3em;
}
.mg-0 {
  margin: 0;
}
.mg-1-5 {
  margin: 0.2em;
}
.mg-2-5 {
  margin: 0.4em;
}
.mg-3-5 {
  margin: 0.6em;
}
.mg-4-5 {
  margin: 0.8em;
}
.mg-1 {
  margin: 1em;
}
.mg-2 {
  margin: 2em;
}
.mgl-1 {
  margin-left: 1em;
}
.mgr-1-5 {
  margin-right: 0.2em;
}
.mgr-2-5 {
  margin-right: 0.4em;
}
.mgr-3-5 {
  margin-right: 0.6em;
}
.mgr-4-5 {
  margin-right: 0.8em;
}
.mgr-1 {
  margin-right: 1em;
}
.mgr-w-1-4 {
  margin-right: 25vw;
}
.mgb-1 {
  margin-bottom: 1em;
}
.mgx-2-5 {
  margin: 0 0.4em;
}
.mgy-1-5 {
  margin: 0.2em 0;
}
.mgy-3-5 {
  margin: 0.6em 0;
}

.hor-around {
  display: flex;
  justify-content: space-around;
}
.hor-between {
  display: flex;
  justify-content: space-between;
}
.mg-center {
  margin: 0 auto;
}

/* 边框 */
.bd {
  border: 1px solid #ccc;
}
.bd-b {
  border-bottom: 0.5px solid #ccc;
}
.underline {
  text-decoration: none;
  border-bottom: 1px solid;
}
.rd-5 {
  border-radius: 5px;
}
.rd-10 {
  border-radius: 10px;
}
.rd-15 {
  border-radius: 15px;
}
.rd-semi {
  border-radius: 50vh; /* 半圆 */
}
.shadow {
  box-shadow: 1px 1px 3px var(--border-shadow);
}

/* 宽度 100 % */
.w-1 {
  width: 1em;
  display: inline-block;
}
.w-4 {
  width: 4.25em;
  display: inline-block;
}
.w-100 {
  width: 100%;
}
.w-70 {
  width: 70%;
}
.h-100 {
  min-height: 90vh;
  height: 100%;
}
/* 居中 */
.center {
  display: flex;
  text-align: center;
  align-items: center;
  justify-content: center;
  margin: 0 auto;
}
.vcenter {
  display: flex;
  align-items: center;
  justify-content: center;
}
.t-center {
  text-align: center;
  justify-content: center;
}
.t-left {
  text-align: left;
  justify-content: start;
}
.t-right {
  text-align: right;
  justify-content: end;
}
.t-vcenter {
  vertical-align: middle;
}
/* 省略号 */
.ellipsis {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}
/* 隐藏 */
.hidden {
  display: none;
}
/* 反光动画 */
.reflect {
  position: relative;
  overflow: hidden;
}
.reflect:hover::after {
  animation: glass-reflection 0.5s ease-out forwards;
  opacity: 0.7;
}

.reflect::after {
  content: '';
  position: absolute;
  pointer-events: none;
  top: 0;
  left: -135%;
  width: 100%;
  height: 100%;
  background: linear-gradient(
    120deg,
    rgba(255, 255, 255, 0) 0%,
    rgba(255, 255, 255, 0.5) 50%,
    rgba(255, 255, 255, 0) 100%
  );
  transform: skewX(-25deg);
}

@keyframes glass-reflection {
  from {
    left: -135%;
  }
  to {
    left: 100%;
  }
}

/* 导航栏 */
nav {
  max-width: 100%;
  margin: 0 auto;
  display: flex;
  justify-content: space-between;
  align-items: center;
}
.nav-logo {
  height: 1.6em;
  vertical-align: middle;
}
.nav-menu {
  box-shadow: 0 0 2px var(--border-shadow);
  background-color: var(--bg-secondary);
}
.menu-left {
  color: var(--blue);
  padding: 0.3em 1em;
}
.menu-left:hover,
.menu-left:focus {
  color: var(--pink);
  border-radius: 50vh;
}
.pure-menu-selected .menu-left {
  color: var(--pink) !important;
}
.menu-right {
  color: gray;
  padding: 0.3em 1em;
}
.menu-right:hover,
.menu-right:focus {
  color: black;
  border-radius: 50vh;
}
.menu-child-container {
  border-radius: 10px;
  box-shadow: 0 0 2px var(--border-shadow);
  left: auto !important;
  right: 3px;
}

/* 移动端底部导航栏 */
.mobile-nav {
  display: none;
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background-color: var(--bg-secondary);
  box-shadow: 0 0 2px var(--border-shadow);
  z-index: 1000;
  padding-top: 0.5em;
  transition: transform 0.3s ease;
}
.mobile-nav .nav-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  color: var(--fg-emp);
  text-decoration: none;
  font-size: 0.8em;
  padding: 0;
}

.mobile-nav .nav-item i {
  font-size: 2em;
  margin-bottom: 2px;
}

.mobile-nav .nav-item.active {
  color: var(--pink);
}

/* 内容区 */
.container {
  width: 100vw;
  max-width: 1200px;
  margin: 0 auto;
  padding: 1em;
}

.panel {
  overflow: hidden;
  max-width: 560px;
  background-color: var(--bg-primary);
  border-radius: 10px;
  box-shadow: 0 0 2px var(--border-shadow);
}
.panel-w {
  overflow: hidden;
  width: 640px;
  max-width: 90vw;
  max-height: 95vh;
  background-color: var(--bg-primary);
  border-radius: 10px;
  box-shadow: 0 0 2px var(--border-shadow);
}

/* 消息体 */
.msg {
  padding: 1em;
  color: white;
  border-radius: 3px;
  position: absolute;
  z-index: 1010;
  opacity: 1;
  top: 1.3em;
  right: 1.3em;
  animation: slideUp 10s ease-in forwards;
}
.msg:hover {
  animation-play-state: paused;
}
.msg a {
  color: white;
  font-weight: 900;
  text-decoration: none;
  border-bottom: 2px solid;
}
.notice {
  background-color: var(--blue);
}
.error {
  background-color: #db4c46;
}
@keyframes slideUp {
  0% {
    transform: translateX(0);
  }
  90% {
    opacity: 1;
  }
  95% {
    transform: translateX(0);
  }
  100% {
    transform: translateX(100%);
    opacity: 0;
  }
}

/* 插画网格  */
.grid-container {
  overflow: hidden;
  margin: 0.6em;
  background-color: var(--bg-secondary);
  border-radius: 12px;
  box-shadow: 0 0 1px var(--border-shadow);
}
.grid-container:hover,
.grid-container:focus {
  box-shadow: 0 0 5px var(--blue);
}

/* 缩略图相关 */
.thumb-container {
  width: 100%;
  position: relative;
  line-height: 0;
}
.thumb-container:hover img,
.thumb-container:focus img {
  transform: scale(1.05);
  transition: ease-in 0.2s;
}
.thumb-cliper {
  overflow: hidden;
  border-radius: 5px;
}
.thumb-square {
  object-fit: cover;
  overflow: hidden;
  aspect-ratio: 1;
  background-color: var(--bg-placeholder);
}
.thumb-ver {
  object-fit: cover;
  overflow: hidden;
  aspect-ratio: 0.618;
  width: 100%;
  height: auto;
  max-height: 100%;
  background-color: var(--bg-placeholder);
}
.thumb-hor {
  object-fit: cover;
  overflow: hidden;
  aspect-ratio: 1.285;
  width: 100%;
  height: auto;
  max-height: 100%;
  background-color: var(--bg-placeholder);
}
.thumb-album {
  object-fit: cover;
  overflow: hidden;
  aspect-ratio: 1.414;
  width: 100%;
  height: auto;
  max-height: 100%;
  background-color: var(--bg-placeholder);
}
.thumb-badge {
  position: absolute;
  color: white;
  width: fit-content;
  text-align: center;
  font-size: 12px;
  font-weight: 700;
  line-height: 1;
  padding: 2px 6px;
  border-radius: 5px;
}
/* 缩略图信息区 */
.thumb-intro {
  padding: 6px;
}
.artist-link {
  display: flex;
  align-items: center;
}
.avatar-mini {
  width: 20px;
  height: 20px;
  padding: 0;
  border-radius: 50vh;
  border: 0.1px solid gray;
  object-fit: cover;
}

/* 多选模式样式 */
.multi-select-controls {
  position: fixed;
  bottom: 20px;
  right: 20px;
  z-index: 1000;
  display: flex;
  gap: 10px;
}

.multi-select-btn {
  color: white;
  border-radius: 50px;
  padding: 12px 20px;
  font-size: 14px;
  font-weight: 700;
  cursor: pointer;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  backdrop-filter: blur(10px);
}

.multi-select-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 16px rgba(0, 0, 0, 0.4);
  border-color: rgba(255, 255, 255, 0.3);
}

.multi-select-btn.cancel-btn {
  background: linear-gradient(135deg, #6c757d, #545b62);
  border-color: rgba(255, 255, 255, 0.2);
}

.multi-select-btn.cancel-btn:hover {
  background: linear-gradient(135deg, #545b62, #3d4142);
  border-color: rgba(255, 255, 255, 0.3);
}

.selection-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: transparent;
  display: flex;
  align-items: flex-end;
  justify-content: flex-end;
  z-index: 10;
  pointer-events: none;
  padding: 6px;
}

.selection-checkbox {
  background: rgba(0, 123, 255, 0.9);
  border: 2px solid white;
  border-radius: 50%;
  width: 26px;
  height: 26px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 12px;
  color: white;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.5);
  pointer-events: auto;
  transition: all 0.2s ease;
  backdrop-filter: blur(4px);
}

.selection-checkbox:hover {
  transform: scale(1.1);
  background: rgba(0, 123, 255, 1);
}

.reflect.selected {
  position: relative;
}

.reflect.selected::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  border-radius: inherit;
  z-index: 5;
  transition: all 0.2s ease;
}

.reflect.selected .selection-checkbox {
  background: rgba(40, 167, 69, 0.95);
  border-color: white;
  z-index: 15;
}

.reflect.selected .selection-checkbox:hover {
  background: rgba(40, 167, 69, 1);
  transform: scale(1.1);
}

/* 确保选择框在图片上方 */
.thumb-container {
  position: relative;
}

.thumb-container .reflect {
  position: relative;
  overflow: hidden;
}

/* 分页样式 */
.pagination {
  width: 100%;
  margin: 20px auto;
  display: flex;
  justify-content: center;
}
.pagination .pure-btn {
  border-radius: 4px;
  font-size: 0.8em;
  font-weight: 700;
}

/* illust content style */
.content {
  background-color: var(--bg-img);
  border-radius: 10px;
  box-shadow: 0 0 2px var(--border-shadow);
  overflow: hidden;
}
.sidebar {
  padding: 1em;
}

.illust {
  max-height: 960px;
  max-width: 100%;
  margin: 0 auto 1em auto;
  background-color: var(--bg-placeholder);
  height: auto;
  display: block;
}
.illust-intro {
  padding: 1.5em;
}
.illust-stats {
  display: flex;
  margin: 0.5em 0;
  align-items: center;
  justify-content: flex-end;
  vertical-align: middle;
}
.illust-stats span {
  margin-left: 1.5em;
}

.btn-shadow:hover,
.btn-shadow:focus {
  transform: translateY(-2px);
  box-shadow: 0 2px 4px rgba(64, 64, 64, 0.5);
}

/* tags style */
.tag {
  padding: 0.6em 1.2em;
  border-radius: 0.5em;
  font-size: 1em;
  line-height: 1;
  text-decoration: none;
  color: white;
}
.tag:hover,
.tag:focus {
  color: white;
  transform: translateY(-2px);
  box-shadow: 0 2px 4px rgba(64, 64, 64, 0.5);
}

.swiper-wrapper {
  position: relative;
  overflow: hidden;
  aspect-ratio: 1.383;
  border-radius: 5px;
}

.swiper {
  position: relative;
  width: 100%;
  height: 100%;
}
.swiper-item {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  opacity: 0;
  visibility: hidden;
  transition: opacity 0.5s ease-in-out;
}
.swiper-item.active {
  opacity: 1;
  visibility: visible;
}
.swiper-item a {
  display: block;
  width: 100%;
  height: 100%;
}
.dots {
  position: absolute;
  bottom: 10px;
  left: 50%;
  transform: translateX(-50%);
  display: flex;
  gap: 8px;
  z-index: 10;
}
.dot {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  border: 1px solid #34495e;
  background: rgba(255, 255, 255, 0.5);
  cursor: pointer;
  transition: background-color 0.3s ease;
}
.dot.active {
  background: white;
}

.tag-cover {
  position: relative;
  overflow: hidden;
  aspect-ratio: 1.4;
  border-radius: 5px;
}
.swiper-item img,
.tag-cover img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}
.tag-cover:hover img {
  transform: scale(1.05);
  transition: ease-in 0.2s;
}
.swiper-item span,
.tag-cover span {
  display: flex;
  position: absolute;
  pointer-events: none;
  bottom: 0;
  left: 0;
  width: 100%;
  height: 1.4em;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 1em;
  font-weight: 700;
  background-color: rgba(0, 0, 0, 0.5);
}
.swiper-item:hover span,
.swiper-item:focus span,
.tag-cover:hover span,
.tag-cover:focus span {
  height: 100%;
  font-size: 1.8em;
  line-height: 2.5em;
  background-color: rgba(0, 0, 0, 0.6);
  transition: ease-in-out 0.1s;
}

/* artists style */
.artist-male {
  color: var(--blue);
}
.artist-female {
  color: var(--pink);
}

.artist-wrapper {
  display: flex;
  padding-right: 0.5em;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 1em;
  gap: 10px;
}
.artist-profile {
  display: flex;
  min-width: 72%;
  align-items: center;
  justify-content: flex-start;
  line-height: 2em;
}
/* 画师头像 */
.avatar {
  width: 3.5em;
  height: 3.5em;
  min-width: 3.5em;
  padding: 2px;
  border-radius: 3.5em;
  border: 1px solid gray;
  object-fit: cover;
}
.avatar-male {
  border: 1px solid var(--blue);
}
.avatar-female {
  border: 1px solid var(--pink);
}

/* 画师最新作品 */
.recent-illusts {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(95px, 1fr));
  gap: 2px;
}
.recent-illusts a {
  display: block;
  padding: 0;
  margin: 0;
  line-height: 0;
}
.recent-illusts img {
  width: 100%;
  height: auto;
  object-fit: cover;
}
.recent-selected {
  opacity: 0.7;
  border: 2px solid var(--blue);
}

/* 模糊效果 */
.blur {
  filter: blur(10px);
  backdrop-filter: blur(10px);
  --webkit-filter: blur(10px);
  --webkit-backdrop-filter: blur(10px);
}
.blur:hover,
.blur:focus {
  filter: blur(5px);
  backdrop-filter: blur(5px);
  --webkit-filter: blur(5px);
  --webkit-backdrop-filter: blur(5px);
}

.top-left {
  top: 0px;
  left: 0px;
}
.top-right {
  top: 0px;
  right: 0px;
}
.bottom-right {
  bottom: 0px;
  right: 0px;
}

/* 画师页面 */
.artist-page {
  display: flex;
  flex-direction: column;
  gap: 2em;
}

/* 标签页 */
.tab-menu {
  margin-bottom: 2em;
}
.tab-menu .pure-menu-link {
  border-radius: 0.5em;
}
.tab-menu .pure-menu-selected {
  border-bottom: 1px solid var(--pink);
}
.pure-menu-selected > .pure-menu-link,
.pure-menu-selected > .pure-menu-link:visited {
  color: var(--fg-primary);
}
.pure-menu-active > .pure-menu-link,
.pure-menu-link:focus,
.pure-menu-link:hover {
  background-color: var(--bg-tertiary);
}
.pure-menu-children {
  background-color: var(--bg-primary);
}

form .sm select {
  padding: 0 0.5em;
  height: 2em;
}

/* 专辑样图 */
.hor-roll {
  display: flex;
  overflow-x: auto;
  height: 80vh;
}
.hor-roll::-webkit-scrollbar {
  width: 10px; /* 设置滚动条的宽度 */
  height: 5px; /* 设置滚动条的高度 */
}
.hor-roll::-webkit-scrollbar-track {
  background: #f1f1f1; /* 设置滚动条的背景颜色 */
}
.hor-roll::-webkit-scrollbar-thumb {
  background: var(--blue); /* 设置滚动条的颜色 */
}
.hor-roll::-webkit-scrollbar-thumb:hover {
  background: var(--pink); /* 设置滚动条悬停时的颜色 */
}

/* 响应式布局 */
@media (max-width: 768px) {
  body {
    font-size: 0.9em;
  }
  footer {
    padding-bottom: 5em;
  }
  h1 {
    font-size: 1.6em;
  }
  h2 {
    font-size: 1.4em;
  }
  h3 {
    font-size: 1.2em;
  }
  h4 {
    font-size: 1em;
  }

  #filterForm label {
    display: inline-block;
  }
  .pure-form-aligned .pure-control-group label {
    display: inline-block;
    text-align: right;
    vertical-align: top;
    width: 20vw;
  }
  .pure-form input[type='email'],
  .pure-form input[type='password'],
  .pure-form input[type='search'],
  .pure-form input[type='text'] {
    display: inline;
  }
  .search-input {
    padding: 4px 8px !important;
    margin: 0 !important;
  }

  .nav-menu {
    font-size: 0.8em;
  }
  .pure-menu-list .menu-left {
    display: none;
  }
  .mobile-nav {
    display: flex;
    justify-content: space-around;
  }
  .pure-menu-horizontal .pure-menu-children {
    top: 2.5em;
    left: -1em;
  }
  .empty {
    display: none;
  }
  .menu-sidebar {
    overflow: hidden;
    position: fixed;
    bottom: 70px;
    right: 14px;
    background-color: rgba(255, 255, 255, 0.8);
    border: 1px solid #ccc;
    border-radius: 10px;
    z-index: 1000;
  }
  .menu-sidebar h3,
  .menu-sidebar span {
    display: none;
  }
  .container {
    padding: 0.3em;
  }
  .recent-illusts {
    grid-template-columns: repeat(auto-fill, minmax(105px, 1fr));
  }
}
@media (max-width: 568px) {
  .thumb-hor {
    aspect-ratio: 1.618;
  }
}

.menu-sidebar .pure-menu-selected a {
  color: var(--pink) !important;
}

.price-info {
  display: flex;
  flex-direction: column;
}

.payment-dialog {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
}

.payment-dialog .panel {
  position: relative;
  width: 90%;
  max-width: 400px;
}
.payment-dialog .ptype-wrapper {
  display: flex;
  width: 7em;
  max-width: 7em;
  align-items: center;
}

.pure-control-group span {
  vertical-align: middle;
}

.i404 {
  display: flex;
  width: 50%;
  border-radius: 25px;
  overflow: hidden;
  text-align: center;
}

.fa-ul {
  margin: 0;
  padding-left: 1em;
}
.fa-ul li {
  position: relative;
  padding: 0.4em;
  list-style-type: none;
}
.fa-ul li::before {
  content: '\f058';
  font-family: 'Font Awesome 6 Free';
  color: var(--skyblue);
  position: absolute;
  left: -1em;
}
.fa-ul li.empty::before {
  display: none;
}
.fa-ul li.new::before {
  font-weight: 900;
  color: var(--blue);
}

.payment-code {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 80%;
  border-radius: 10px;
}

.relative {
  position: relative;
}

.close-btn {
  position: absolute;
  top: 10px;
  right: 10px;
  font-size: 1.5em;
  cursor: pointer;
  color: #888;
}

.close-btn:hover {
  color: #333;
}

.notice-marquee-wrapper {
  display: flex;
  overflow: hidden;
  width: 100%;
  height: 2em;
  background: var(--bg-tertiary);
  border-radius: 10px;
  align-items: center;
}
.notice-marquee {
  display: inline-block;
  white-space: nowrap;
  font-weight: 700;
  font-size: 1em;
  animation: notice-marquee-scroll 15s linear infinite;
  will-change: transform;
}
@keyframes notice-marquee-scroll {
  0% {
    transform: translateX(100%);
  }
  100% {
    transform: translateX(-100vw);
  }
}

.placeholder {
  display: flex;
}

#searchContainer {
  position: relative;
  overflow: visible !important;
}

.hot-search-dropdown {
  display: none;
  position: absolute;
  top: 100%;
  left: 0;
  background-color: var(--bg-primary);
  border-radius: 10px;
  border: 1px solid var(--bg-tertiary);
  box-shadow: 0 0 1px var(--border-shadow);
  z-index: 1000;
  width: 100%;
  max-height: 300px;
  overflow-y: auto;
}

.hot-search-dropdown a,
.hot-search-item {
  display: block;
  padding: 8px 12px;
  color: var(--fg-primary);
  text-decoration: none;
  cursor: pointer;
  transition: background-color 0.2s ease;
  border: none;
  background: none;
  width: 100%;
  text-align: left;
  font-size: inherit;
  font-family: inherit;
}

.hot-search-dropdown a:hover,
.hot-search-item:hover,
.hot-search-dropdown a.active,
.hot-search-item.active {
  background-color: var(--bg-tertiary);
}

.hot-search-dropdown a:focus,
.hot-search-item:focus {
  outline: 2px solid var(--accent-color);
  outline-offset: -2px;
}

.hot-search-loading {
  display: block;
  padding: 8px 12px;
  color: var(--fg-secondary);
  text-align: center;
  font-size: 0.8em;
}

.hot-search-loading::before {
  content: '⏳ ';
  animation: spin 1s linear infinite;
}

@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}
