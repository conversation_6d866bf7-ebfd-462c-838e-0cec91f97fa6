// 在 input 标签上显示错误信息
function inputReport(input, message) {
  input.setCustomValidity(message);
  return input.reportValidity(); // 触发验证并显示错误信息
}

// 检查邮箱格式
function emailValidation(emailInput) {
  emailInput.setCustomValidity(''); // 清除之前的自定义验证状态

  // RFC 5322 邮箱格式正则表达式
  const emailRegex =
    /^(([^<>()[\]\\.,;:\s@"]+(\.[^<>()[\\]\\.,;:\s@"]+)*)|(".+"))@((\[[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}])|(([a-zA-Z\-0-9]+\.)+[a-zA-Z]{2,}))$/;

  if (!emailRegex.test(emailInput.value.trim())) {
    return inputReport(emailInput, '请输入有效的邮箱地址');
  }
  return emailInput.reportValidity();
}

// 检查密码强度
function chkPassword(passwordInput, minLength) {
  passwordInput.setCustomValidity(''); // 清除之前的自定义验证状态

  var password = passwordInput.value.trim();
  var upper = /[A-Z]/;
  var number = /[0-9]/;

  if (password.length < minLength) {
    return inputReport(passwordInput, `密码长度不能少于 ${minLength} 个字符`);
  } else if (!upper.test(password) || !number.test(password)) {
    return inputReport(passwordInput, '密码必须包含大写字母和数字');
  } else {
    return true;
  }
}

// 检查密码和确认密码是否一致
function chkConfirm(passwordInput, confirmInput) {
  confirmInput.setCustomValidity(''); // 清除之前的自定义验证状态

  if (passwordInput.value.trim() !== confirmInput.value.trim()) {
    inputReport(confirmInput, '两次输入的密码不一致');
    return false;
  } else {
    return true;
  }
}

// 获取 cookie
function getCookie(name) {
  const value = `; ${document.cookie}`;
  const parts = value.split(`; ${name}=`);
  if (parts.length === 2) {
    return parts.pop().split(';').shift();
  }
}

// 从 URL 中获取通知信息
function noticeFromURL() {
  // 提取 msg 和 err 参数
  const url = new URL(window.location.href);
  const msg = url.searchParams.get('msg');
  const err = url.searchParams.get('err');
  // 删除 msg 和 err 参数, 并发出通知
  if (msg) {
    notyf.success(msg);
    url.searchParams.delete('msg');
  }
  if (err) {
    notyf.error(err);
    url.searchParams.delete('err');
  }
  // 替换当前 URL
  window.history.replaceState({}, '', url.toString());
}

// 发送 API 请求
async function fetchAPI(method, url, data = null, handleResponse = null) {
  const options = {
    method: method,
    headers: {},
    credentials: 'same-origin', // 添加凭证
    redirect: 'follow'
  };

  if (method === 'POST') {
    options.headers['Content-Type'] = 'application/x-www-form-urlencoded';
    options.body = new URLSearchParams(data);
  } else if (method === 'GET' && data) {
    url += '?' + new URLSearchParams(data);
  }

  return fetch(url, options)
    .then(async (response) => {
      const contentType = response.headers.get('Content-Type');

      if (response.redirected) {
        // 处理重定向
        window.history.pushState({}, '', response.url);
        return response.text().then((content) => {
          document.body.innerHTML = content;
          // 如果页面包含脚本，需要重新执行它们
          for (let script of document.body.getElementsByTagName('script')) {
            const newScript = document.createElement('script');
            newScript.text = script.text;
            if (script.src) {
              newScript.src = script.src;
            }
            script.parentNode.replaceChild(newScript, script);
          }
        });
      } else if (contentType && contentType.includes('application/json')) {
        // 处理 JSON 响应
        let jsonData = await response.json();
        if (jsonData.rc === 0) {
          if (jsonData.msg) {
            notyf.success(jsonData.msg);
          }
          if (jsonData.next_url) {
            window.location.href = jsonData.next_url;
          }
          if (jsonData.new_url) {
            window.open(jsonData.new_url, '_blank'); // 在新页面打开链接
          }
        } else {
          notyf.error(jsonData.msg || '操作失败');
        }

        return handleResponse ? handleResponse(jsonData) : jsonData;
      } else {
        const htmlData = await response.text();
        return handleResponse ? handleResponse(htmlData) : htmlData;
      }
    })
    .catch((error) => {
      console.error('请求失败:', error);
    });
}

// 惰性加载下一页内容
function lazyLoadNextPage(containerId) {
  let loading = false;
  let noMoreData = false;
  let currentPage = parseInt(new URLSearchParams(window.location.search).get('page')) || 1;
  let container = document.getElementById(containerId);

  // 获取下一页内容
  async function fetchNextPage() {
    if (loading || noMoreData) return;

    loading = true;

    // 获取当前 URL 并处理 page 参数
    const url = new URL(window.location.href);
    const params = new URLSearchParams(url.search);
    params.set('page', ++currentPage);
    // 提取 nsp 参数
    let nsp = container.getAttribute('data-nsp');
    if (nsp in ['0', '1']) params.set('sp', nsp);

    try {
      // 获取下一页内容
      const response = await fetch(`${url.pathname}?${params.toString()}`);
      const html = await response.text();

      // 解析 HTML 并提取 containerId 对应的控件内容，并追加到当前页面
      const parser = new DOMParser();
      const newDoc = parser.parseFromString(html, 'text/html');
      const newContainer = newDoc.getElementById(containerId);
      if (container && newContainer) {
        container.insertAdjacentHTML('beforeend', newContainer.innerHTML);
      }

      // 更新 nsp 属性
      nsp = newContainer.getAttribute('data-nsp');
      if (nsp in ['0', '1']) container.setAttribute('data-nsp', nsp);

      // 检查是否有 noMore 元素或 containerId 对应的控件为空
      if (newDoc.getElementById('noMore') || newDoc.querySelector('#' + containerId).children.length === 0) {
        noMoreData = true;
        window.removeEventListener('scroll', handleScroll);
      }
    } catch (error) {
      console.error('加载下一页失败:', error);
    } finally {
      loading = false;
    }
  }

  function handleScroll() {
    if (window.innerHeight + window.scrollY >= document.documentElement.scrollHeight - 100) {
      fetchNextPage();
    }
  }

  window.addEventListener('scroll', handleScroll); // 监听滚动事件
  if (!document.getElementById('noMore') && window.innerHeight >= document.documentElement.scrollHeight) {
    fetchNextPage();
  }
}

// --- 热门搜索功能 ---
function initHotSearch() {
  const searchContainer = document.getElementById('searchContainer');
  const searchForm = document.getElementById('searchForm');

  // 确保所有需要的元素都存在
  if (!searchContainer || !searchForm) return;

  const searchInput = searchForm.querySelector('input[name="kw"]');
  const searchTypeInput = searchForm.querySelector('input[name="stype"]');
  const resultsContainer = document.getElementById('hot-search-results');

  if (!searchInput || !searchTypeInput || !resultsContainer) return;

  let isDropdownVisible = false;
  let fetchTimeout = null;
  let isLoading = false;
  let cachedResults = new Map(); // 缓存不同搜索类型的结果

  // 获取热门搜索词并显示
  const fetchAndShowHotWords = async () => {
    const stype = searchTypeInput.value || 'illust';

    // 防止重复请求
    if (isLoading) return;

    // 检查缓存
    if (cachedResults.has(stype)) {
      const cachedWords = cachedResults.get(stype);
      displayHotWords(cachedWords);
      return;
    }

    isLoading = true;

    // 显示加载状态
    resultsContainer.innerHTML = '<div class="hot-search-loading">正在加载热门搜索词...</div>';
    showDropdown();

    try {
      const response = await fetchAPI('GET', `/a/search/hot/${stype}/`);

      // 检查响应格式：jsonify 装饰器会返回 {rc: 0, data: [...]}
      let hotWords = [];
      if (response && response.rc === 0 && response.data) {
        hotWords = response.data;
      } else if (Array.isArray(response)) {
        // 兼容直接返回数组的情况
        hotWords = response;
      }

      // 缓存结果（5分钟有效期）
      cachedResults.set(stype, hotWords);
      setTimeout(() => cachedResults.delete(stype), 5 * 60 * 1000);

      displayHotWords(hotWords);
    } catch (error) {
      console.error('获取热门搜索词失败:', error);
      hideDropdown();
    } finally {
      isLoading = false;
    }
  };

  // 显示热门搜索词
  const displayHotWords = (hotWords) => {
    if (hotWords.length > 0) {
      resultsContainer.innerHTML = ''; // 清空旧结果

      hotWords.forEach((word) => {
        const item = document.createElement('a');
        item.className = 'hot-search-item';
        item.textContent = word;
        item.href = '#';

        // 使用 mousedown 代替 click，因为它在 blur 之前触发
        item.addEventListener('mousedown', (e) => {
          e.preventDefault(); // 防止输入框失去焦点
          searchInput.value = word;
          hideDropdown();
          searchForm.submit();
        });

        resultsContainer.appendChild(item);
      });

      showDropdown();
    } else {
      hideDropdown();
    }
  };

  // 显示下拉菜单
  const showDropdown = () => {
    if (!isDropdownVisible) {
      resultsContainer.style.display = 'block';
      isDropdownVisible = true;
    }
  };

  // 隐藏下拉菜单
  const hideDropdown = () => {
    if (isDropdownVisible) {
      resultsContainer.style.display = 'none';
      isDropdownVisible = false;
    }
  };

  // 延迟获取热门搜索词（防抖）
  const debouncedFetch = () => {
    if (fetchTimeout) {
      clearTimeout(fetchTimeout);
    }
    fetchTimeout = setTimeout(fetchAndShowHotWords, 200);
  };

  // 事件监听器
  // 当输入框获得焦点时触发
  searchInput.addEventListener('focus', () => {
    debouncedFetch();
  });

  // 当鼠标悬停在搜索容器上时触发
  searchContainer.addEventListener('mouseenter', () => {
    if (document.activeElement === searchInput) {
      debouncedFetch();
    }
  });

  // 当输入框失去焦点时隐藏下拉菜单（延迟执行，给点击事件时间）
  searchInput.addEventListener('blur', () => {
    setTimeout(hideDropdown, 150);
  });

  // 当点击页面其他地方时，隐藏下拉菜单
  document.addEventListener('click', (event) => {
    if (!searchContainer.contains(event.target)) {
      hideDropdown();
    }
  });

  // 键盘导航支持
  searchInput.addEventListener('keydown', (e) => {
    if (!isDropdownVisible) return;

    const items = resultsContainer.querySelectorAll('.hot-search-item');
    let currentIndex = -1;

    // 找到当前选中的项
    items.forEach((item, index) => {
      if (item.classList.contains('active')) {
        currentIndex = index;
      }
    });

    if (e.key === 'ArrowDown') {
      e.preventDefault();
      // 移除当前选中状态
      if (currentIndex >= 0) {
        items[currentIndex].classList.remove('active');
      }
      // 选中下一项
      currentIndex = (currentIndex + 1) % items.length;
      items[currentIndex].classList.add('active');
    } else if (e.key === 'ArrowUp') {
      e.preventDefault();
      // 移除当前选中状态
      if (currentIndex >= 0) {
        items[currentIndex].classList.remove('active');
      }
      // 选中上一项
      currentIndex = currentIndex <= 0 ? items.length - 1 : currentIndex - 1;
      items[currentIndex].classList.add('active');
    } else if (e.key === 'Enter' && currentIndex >= 0) {
      e.preventDefault();
      // 选择当前高亮的项
      searchInput.value = items[currentIndex].textContent;
      hideDropdown();
      searchForm.submit();
    } else if (e.key === 'Escape') {
      hideDropdown();
    }
  });
}
