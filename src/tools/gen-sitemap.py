#!/usr/bin/env python

"""
Sitemap 生成主脚本
"""

import asyncio
import datetime
import json
import math
import sys
from collections.abc import Iterable
from pathlib import Path

from tortoise import Tortoise

# 添加项目目录到系统路径
PROJECT_DIR = Path(__file__).absolute().parent.parent
sys.path.append(PROJECT_DIR.as_posix())

from apps.album.models import Album
from apps.pixiv.models import Artist, Illust, Tag
from config import DOMAIN
from libs.orm import Model
from libs.terminal import print_dbg, print_inf, print_ok

OUTPUT_DIR = PROJECT_DIR / 'sitemap'
STATE_FILE = OUTPUT_DIR / 'sitemap_state.json'
PAGE_SIZE = 10000  # 每个 sitemap 文件包含的 URL 数量

# 模型与 URL 前缀及增量策略映射
# (模型, URL前缀, 增量策略: 'id' 或 'added')
MODEL_CONFIG = [
    (Artist, 'artist', 'added'),
    (Illust, 'illust', 'added'),
    (Tag, 'tag', 'id'),
    (Album, 'album', 'id'),
]

BASE_URL = f'https://www.{DOMAIN}'


def load_state() -> dict:
    """加载状态文件"""
    if not STATE_FILE.exists():
        return {}
    with STATE_FILE.open('r', encoding='utf-8') as f:
        try:
            return json.load(f)
        except json.JSONDecodeError:
            return {}


def save_state(state: dict):
    """保存状态到文件"""
    with STATE_FILE.open('w', encoding='utf-8') as f:
        json.dump(state, f, indent=4)


def create_sitemap_index(sitemap_urls: Iterable[str]) -> str:
    """
    创建 Sitemap 索引文件内容.

    :param sitemap_urls: 所有子 sitemap 的完整 URL 列表.
    :return: Sitemap 索引的 XML 字符串.
    """
    parts = ['<sitemapindex xmlns="http://www.sitemaps.org/schemas/sitemap/0.9">']
    parts.extend(
        f'\t<sitemap>\n\t\t<loc>{url}</loc>\n\t\t<lastmod>{datetime.date.today().isoformat()}</lastmod>\n\t</sitemap>'
        for url in sitemap_urls
    )
    parts.append('</sitemapindex>')
    return '\n'.join(parts)


def create_sitemap(urls: Iterable[str]) -> str:
    """
    创建单个 Sitemap 文件内容.

    :param urls: 页面 URL 列表.
    :return: Sitemap 的 XML 字符串.
    """
    parts = [
        '<urlset xmlns="http://www.sitemaps.org/schemas/sitemap/0.9"'
        ' xmlns:image="http://www.google.com/schemas/sitemap-image/1.1">',
    ]
    parts.extend(f'\t<url><loc>{url}</loc></url>' for url in urls)
    parts.append('</urlset>')
    return '\n'.join(parts)


async def generate_for_model(model: Model, path_prefix: str, strategy: str, last_value: int | str | None):
    """
    为指定的模型增量生成 sitemap 文件.

    :param model: 数据模型.
    :param path_prefix: URL 路径前缀.
    :param strategy: 增量策略 ('id' 或 'added').
    :param last_value: 上次记录的值 (ID 或日期/时间字符串).
    :return: 一个元组 (新生成的 sitemap URL 列表, 新的 last_value).
    """
    model_name = model.__name__
    print_inf(f'检查 {model_name} 中的新增数据...')

    query = model.all()
    if strategy == 'id' and last_value:
        query = query.filter(id__gt=int(last_value))
    elif strategy == 'added' and last_value:
        last_run_dt = datetime.datetime.fromisoformat(str(last_value))
        query = query.filter(added__gt=last_run_dt)

    # 根据策略获取数据并确定新的 last_value
    if strategy == 'added':
        # 获取 (id, added) 元组列表
        fetched_data = await query.order_by('id').values_list('id', 'added')
        if not fetched_data:
            print_dbg(f'{model_name} 中没有新增数据, 跳过.\n', flash=False)
            return [], last_value

        new_items_ids = [item[0] for item in fetched_data]
        max_added_dt = max(item[1] for item in fetched_data)
        new_last_value = max_added_dt.isoformat()
    else:
        # 获取 id 列表
        new_items_ids = await query.order_by('id').values_list('id', flat=True)
        if not new_items_ids:
            print_dbg(f'{model_name} 中没有新增数据, 跳过.\n', flash=False)
            return [], last_value
        new_last_value = max(new_items_ids)

    total_new = len(new_items_ids)
    print_inf(f'发现 {total_new} 条新数据, 开始生成 sitemap 文件.')

    sitemap_urls = []
    num_pages = math.ceil(total_new / PAGE_SIZE)
    today_str = datetime.date.today().isoformat()

    for page in range(num_pages):
        start = page * PAGE_SIZE
        end = start + PAGE_SIZE
        page_items = new_items_ids[start:end]

        page_urls = [f'{BASE_URL}/w/{path_prefix}/{item_id}' for item_id in page_items]

        # 为避免文件名冲突和覆盖, 使用日期和页码命名
        sitemap_filename = f'sitemap_{model_name.lower()}_{today_str}_{page + 1}.xml'
        sitemap_content = create_sitemap(page_urls)
        (OUTPUT_DIR / sitemap_filename).write_text(sitemap_content, encoding='utf-8')

        sitemap_urls.append(f'{BASE_URL}/{sitemap_filename}')
        print_dbg(f'  - 已生成文件: {sitemap_filename}', flash=False)

    print_ok(f'{model_name} 的 sitemap 生成完毕.\n')
    return sitemap_urls, new_last_value


async def generate_core_sitemap():
    """生成核心页面的 sitemap"""
    print_inf('正在生成核心页面 sitemap (sitemap_core.xml)...')
    core_urls = [
        f'{BASE_URL}/',
        f'{BASE_URL}/w/illust/',
        f'{BASE_URL}/w/artist/',
        f'{BASE_URL}/w/album/',
        f'{BASE_URL}/w/tag/',
        f'{BASE_URL}/w/purchase/',
    ]
    sitemap_content = create_sitemap(core_urls)
    sitemap_filename = 'sitemap_core.xml'
    (OUTPUT_DIR / sitemap_filename).write_text(sitemap_content, encoding='utf-8')
    print_ok('核心页面 sitemap 已生成.\n')
    return f'{BASE_URL}/{sitemap_filename}'


async def main():
    """主函数"""
    print_inf('开始生成 Sitemap ...\n')
    if not OUTPUT_DIR.exists():
        OUTPUT_DIR.mkdir()

    # 定义本次运行的开始时间, 用于记录 last_run
    run_dt_iso = datetime.datetime.now(datetime.UTC).isoformat()

    state = load_state()
    await Model.init_db()

    # 1. 查找所有已存在的 sitemap 文件 (除了索引和核心文件)
    existing_sitemaps = [f for f in OUTPUT_DIR.glob('sitemap_*.xml') if f.name != 'sitemap_core.xml']
    all_sitemap_urls = [f'{BASE_URL}/{f.name}' for f in existing_sitemaps]

    # 2. 生成核心 sitemap (每次都重新生成)
    core_sitemap_url = await generate_core_sitemap()
    # 确保核心 sitemap url 只在列表中出现一次
    if core_sitemap_url not in all_sitemap_urls:
        all_sitemap_urls.append(core_sitemap_url)

    # 3. 为各个模型生成增量 sitemap
    for model, prefix, strategy in MODEL_CONFIG:
        model_name = model.__name__
        last_value = state.get(model_name, {}).get('last_value')

        new_sitemap_urls, new_last_value = await generate_for_model(model, prefix, strategy, last_value)

        if new_sitemap_urls:
            all_sitemap_urls.extend(new_sitemap_urls)
            if model_name not in state:
                state[model_name] = {}
            state[model_name]['last_value'] = new_last_value
            state[model_name]['last_run'] = run_dt_iso

    # 4. 生成 sitemap 索引文件
    print_inf('正在重新生成 sitemap 索引文件 (sitemap.xml)...')
    # 去重并排序, 保证索引文件的一致性
    unique_urls = sorted(set(all_sitemap_urls))
    index_content = create_sitemap_index(unique_urls)
    (OUTPUT_DIR / 'sitemap.xml').write_text(index_content, encoding='utf-8')

    # 5. 保存新的状态
    save_state(state)

    await Tortoise.close_connections()

    print_ok('Sitemap 索引文件已更新.\n')
    print_ok('Sitemap 文件生成完毕!')


if __name__ == '__main__':
    asyncio.run(main())
