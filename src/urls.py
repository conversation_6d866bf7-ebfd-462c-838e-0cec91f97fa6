from fastapi import FastAPI

import apps.album.views as v_album
import apps.payment.views as v_payment
import apps.pixiv.views as v_pixiv
import apps.user.apis as a_user
import apps.user.views as v_user
from apps import privacy
from apps.pixiv import apis as p_api
from libs.http import jsonify

# from common.utils import timer


URLS: list[tuple] = [
    # Pixiv
    ('GET', '/', v_pixiv.home),
    ('GET', '/w/search/', v_pixiv.search),
    ('GET', '/a/search/hot/{stype}/', p_api.hot_words, jsonify),
    ('GET', '/w/illust/', v_pixiv.illust_rcmd),
    ('GET', '/w/illust/{iid}', v_pixiv.illust),
    ('GET', '/a/illust/download/{iid}', v_pixiv.download_illust, jsonify),
    # ('GET', '/a/pixiv/illust/{iid}', p_api.idetail, jsonify),
    ('GET', '/w/irelated/{iid}', v_pixiv.irelated),
    ('GET', '/w/artist/', v_pixiv.artist_rcmd),
    ('GET', '/w/artist/{aid}', v_pixiv.artist),
    ('GET', '/w/tag/', v_pixiv.tag_rcmd),
    ('GET', '/w/tag/{tid}', v_pixiv.tag),
    ('GET', '/w/album/', v_album.album_rcmd),
    ('GET', '/w/album/{alb_id}', v_album.album),
    ('GET', '/w/album/purchase/{alb_id}', v_album.purchase_album),
    # User
    ('GET', '/w/register/', v_user.signup),
    ('POST', '/w/register/', v_user.signup, jsonify),
    ('GET', '/a/register/vcode/', a_user.send_signup_verify_code, jsonify),
    ('GET', '/w/login/', v_user.signin),
    ('POST', '/w/login/', v_user.signin),
    ('GET', '/w/reset/password/', v_user.reset_password),
    ('POST', '/w/reset/password/', v_user.reset_password, jsonify),
    ('GET', '/a/reset/vcode/', a_user.send_reset_verify_code, jsonify),
    ('GET', '/w/user/logout/', v_user.signout),
    ('GET', '/w/user/profile/', v_user.profile),
    ('POST', '/w/user/profile/', v_user.profile, jsonify),
    ('GET', '/w/user/illusts/', v_user.purchased_illusts),
    ('GET', '/w/user/albums/', v_user.purchased_albums),
    ('GET', '/w/user/checkin/', v_user.checkin, jsonify),
    ('GET', '/w/user/followed/', v_user.followed),
    ('POST', '/a/user/follow/{aid}', v_user.follow, jsonify),
    ('POST', '/a/user/unfollow/{aid}', v_user.unfollow, jsonify),
    ('GET', '/w/user/favorited/', v_user.favorited),
    ('POST', '/a/user/favorite/{iid}', v_user.favorite, jsonify),
    ('POST', '/a/user/unfavorite/{iid}', v_user.unfavorite, jsonify),
    ('GET', '/w/user/ad/award/', v_user.ad_award),
    # Payment
    ('GET', '/w/purchase/', v_payment.products),
    ('GET', '/w/purchase/vip/{vid}/', v_payment.purchase_vip),
    ('GET', '/w/purchase/coin/{cid}/', v_payment.purchase_coin),
    # ('GET', '/w/purchase/album/{aid}/', v_payment.purchase_album),
    ('GET', '/a/purchase/callback/', v_payment.purchase_callback, jsonify),
    ('GET', '/w/purchase/result/', v_payment.purchase_result),
    # privacy
    ('GET', '/w/privacy/', privacy),
]


def register_urls(app: FastAPI, urls: list):
    """绑定 URL 与 视图函数"""
    for method, url_path, view_fn, *decos in urls:
        http_method = getattr(app, method.lower())
        fn_bind = http_method(url_path)

        # 用指定的装饰器包装视图函数
        for deco in decos:
            view_fn = deco(view_fn)

        # 绑定视图函数与路由
        fn_bind(view_fn)
