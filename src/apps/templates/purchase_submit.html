{% extends 'base.html' %}

{% block left_side %}
  <div class="center pure-g pd-5">
    <h2 class="pure-u-1">正在提交订单</h2>
    <div class="pure-u-1 pd-1">
      <i class="fa-solid fa-spinner fa-spin-pulse fa-2xl"></i>
    </div>
    <div class="pure-u-1 pd-2">
      <button class="pure-button rd-5 sm"
              onclick="javascript:history.back();">取消</button>
    </div>
  </div>
  <form id="paySubmit"
        method="post"
        action="{{ cfg.PAY_SUBMIT_API }}">
    {% for key, value in payment_params.items() %}
      <!-- 订单参数 -->
      <input type="hidden" name="{{ key }}" value="{{ value }}" />
    {% endfor %}
  </form>
{% endblock %}

{% block extra_js %}
  <script type="text/javascript">
    document.getElementById('paySubmit').submit(); // 自动提交表单
  </script>
{% endblock %}
