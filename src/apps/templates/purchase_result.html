{% extends 'base.html' %}

{% block left_side %}
  <div class="container">
    <div class="panel mg-center pd-2">
      {% if order %}
        <h2 class="t-center">购买成功</h2>
        <p class="t-center pd-2 xl">
          <i class="fa-regular fa-circle-check fa-2xl fg-green"></i>
        </p>
        {% if order.goods == 'vip' %}
          <!-- 购买 Vip 结果 -->
          <form class="pure-form pure-form-aligned">
            <fieldset>
              <div class="pure-control-group fg-emp">
                <label>&emsp;</label>
                <span class="b-900 lg">当前 VIP 状态</span>
              </div>
              <div class="pure-control-group">
                <label>
                  <i class="fa-solid fa-crown fg-golden"></i>
                </label>
                <span class="b-900 fg-orange">{{ user.vip.name }}</span>
              </div>
              <div class="pure-control-group">
                <label>
                  <i class="fa-solid fa-clock-rotate-left"></i>
                </label>
                <span class="b-900">{{ user.vend.date() }} 到期</span>
              </div>
            </div>
          </fieldset>
        </form>
      {% elif order.goods == 'coin' %}
        <!-- 购买 Coin 结果 -->
        <form class="pure-form pure-form-aligned">
          <fieldset>
            <div class="pure-control-group">
              <label>
                <i class="fa-solid fa-coins fg-golden"></i>
              </label>
              <span class="b-900">当前共有 {{ user.coins }} 枚次元币</span>
            </div>
          </fieldset>
        </form>
      {% elif order.goods == 'album' %}
        <!-- 购买 Album 结果 -->
        <p class="t-center">
          <i class="fa-solid fa-circle-info mgr-2-5 fg-emp"></i>
          专辑 「<a class="b-700 fg-blue" href="/w/album/{{ order.gid }}">{{ payment_info.name }}</a>」 已放入您的账户！
          <a href="/w/user/albums/">点击这里</a> 查看。
        </p>
      {% else %}
        <p class="t-center">
          <i class="fa-solid fa-circle-exclamation fg-red mgr-2-5"></i>未知的商品类型
        </p>
      {% endif %}
    {% else %}
      <!-- 购买失败结果 -->
      <h2 class="t-center">交易失败</h2>
      <p class="t-center mg-2 pd-2 xl">
        <i class="fa-solid fa-circle-exclamation fa-shake fa-2xl fg-red"></i>
      </p>
    {% endif %}
  </div>
{% endblock %}
