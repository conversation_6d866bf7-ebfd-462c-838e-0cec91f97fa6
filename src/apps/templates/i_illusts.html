{% if enable_filter %}
  <form id="filterForm" class="pure-form pure-g w-100" method="get">
    <fieldset class="sm">
      <label>排序：</label>
      <select id="order" name="order" class="mgr-1">
        <option value="default" selected>默认</option>
        <option value="created">发布时间</option>
        <option value="n_view">观看量</option>
        <option value="n_bookmark">点赞量 🔥</option>
      </select>

      <label>版式：</label>
      <select id="aspect" name="aspect" class="mgr-1">
        <option value="all" selected>全部</option>
        <option value="ver">竖图</option>
        <option value="hor">横图</option>
        <option value="sqr">方图</option>
      </select>

      <label>𝑺 值：</label>
      <select id="ss" name="ss" class="mgr-1">
        <option value="all" selected>默认</option>
        <option value="2">萌新</option>
        <option value="4">青涩</option>
        <option value="6">绅士</option>
      </select>
    </fieldset>
  </form>
{% endif %}

<div id="illustsContainer"
     class="pure-g w-100"
     data-nsp="{{ sp }}">
  {% if cfg.ENABLE_NATIVE_BANNER_ADULT_AD and ad and not (user and user.has_perms('disable_ad')) %}
    <div class="pure-u-1 pure-u-sm-1-2 pure-u-md-1-3">
      <div class="grid-container sm">
        <div class="thumb-container">{% include "ad_native_banner_adult.html" %}</div>
      </div>
    </div>
  {% endif %}

  {% for illust in illusts %}
    {% if illust.aspect <= 1.1 %}
      <div class="pure-u-1-2 pure-u-sm-1-4 pure-u-md-1-6">
        <div class="grid-container sm">
          <div class="thumb-container" data-illust-id="{{ illust.id }}">
            <!-- 缩略图 -->
            <div class="reflect">
              <a href="/w/illust/{{ illust.id }}"
                 title="#{{ illust.main_tag }}: {{ illust.title }}"
                 class="illust-link">
                <img class="thumb-ver"
                     src="{{ illust.thumbnail }}"
                     loading="lazy"
                     alt="#{{ illust.main_tag }} {{ illust.title }} - {{ (illust.artist or artist).name }} 的插画作品" />
              </a>
              <!-- 多选模式的选择框 -->
              <div class="selection-overlay" style="display: none;">
                <div class="selection-checkbox">
                  <i class="fa-solid fa-plus"></i>
                </div>
              </div>
            </div>
            {% include "i_badge.html" %}
          </div>
          {% if cfg.THUMBNAIL_INTRO or show_intro %}
            <div class="thumb-intro ellipsis">
              <!-- 插画信息 -->
              <a href="/w/illust/{{ illust.id }}" class="fg-emp b-700 mg-0">{{ illust.title }}</a>
              {% if illust.artist %}
                <a class="artist-link fg-gray"
                   href="/w/artist/{{ illust.artist.id }}"
                   title="画师 {{ illust.artist.name }} 的主页">
                  <img src="{{ illust.artist.avturl }}"
                       loading="lazy"
                       class="avatar-mini mgr-2-5"
                       alt="画师: {{ illust.artist.name }}" />
                  <span class="ellipsis">{{ illust.artist.name }}</span>
                </a>
              {% endif %}
            </div>
          {% endif %}
        </div>
      </div>
    {% else %}
      <div class="pure-u-1 pure-u-sm-1-2 pure-u-md-1-3">
        <div class="grid-container sm">
          <div class="thumb-container" data-illust-id="{{ illust.id }}">
            <!-- 缩略图 -->
            <div class="reflect">
              <a href="/w/illust/{{ illust.id }}"
                 title="#{{ illust.main_tag }}: {{ illust.title }}"
                 class="illust-link">
                <img class="thumb-hor"
                     src="{{ illust.thumbnail }}"
                     loading="lazy"
                     alt="#{{ illust.main_tag }} {{ illust.title }} - {{ (illust.artist or artist).name }} 的插画作品" />
              </a>
              <!-- 多选模式的选择框 -->
              <div class="selection-overlay" style="display: none;">
                <div class="selection-checkbox">
                  <i class="fa-solid fa-plus"></i>
                </div>
              </div>
            </div>
            {% include "i_badge.html" %}
          </div>
          {% if cfg.THUMBNAIL_INTRO or show_intro %}
            <div class="thumb-intro">
              <!-- 插画信息 -->
              <a href="/w/illust/{{ illust.id }}"
                 class="fg-emp b-700 mg-0 ellipsis">{{ illust.title }}</a>
              {% if illust.artist %}
                <a class="ellipsis artist-link fg-gray"
                   href="/w/artist/{{ illust.artist.id }}"
                   title="画师 {{ illust.artist.name }} 的主页">
                  <img src="{{ illust.artist.avturl }}"
                       loading="lazy"
                       class="avatar-mini mgr-2-5"
                       alt="画师: {{ illust.artist.name }}" />
                  <span class="ellipsis">{{ illust.artist.name }}</span>
                </a>
              {% endif %}
            </div>
          {% endif %}
        </div>
      </div>
    {% endif %}
  {% else %}
    {% include "no_more.html" %}
  {% endfor %}
</div>

<!-- 多选模式控制按钮 -->
{% if not disable_dynamic %}
  <div id="multiSelectControls" class="multi-select-controls">
    <button id="multiSelectBtn"
            class="multi-select-btn bg-dazzling rd-semi"
            onclick="toggleMultiSelectMode()">
      <i class="fa-solid fa-check-square mgr-2-5"></i>
      多选
    </button>
  </div>
{% endif %}

{% block extra_js %}
  {% if enable_filter %}
    <!-- 过滤器 -->
    <script type="text/javascript">
      // 初始化选中状态
      const args = ['order', 'aspect', 'ss'];
      const urlParams = new URLSearchParams(window.location.search);
      for (const arg of args) {
        if (urlParams.has(arg)) {
          document.getElementById(arg).value = urlParams.get(arg);
        }
      }

      // 监听filterForm变化
      const filterForm = document.getElementById('filterForm');
      if (filterForm) {
        filterForm.addEventListener('change', function(e) {
          if (e.target.tagName === 'SELECT') {
            const url = new URL(window.location.href);
            url.searchParams.set(e.target.name, e.target.value);
            window.location.href = url.toString();
          }
        })
      };
    </script>
  {% endif %}

  {% if not disable_dynamic %}
    <!-- 多图下载 -->
    <script type="text/javascript" src="{{ static('js/download.js') }}"></script>
    <script type="text/javascript">
      // 多选模式相关变量
      let isMultiSelectMode = false;
      let selectedIllusts = new Set();
      const maxSelection = 50;

      // 切换多选模式
      function toggleMultiSelectMode() {
        isMultiSelectMode = !isMultiSelectMode;
        const btn = document.getElementById('multiSelectBtn');
        const controls = document.getElementById('multiSelectControls');
        const overlays = document.querySelectorAll('.selection-overlay');

        if (isMultiSelectMode) {
          // 进入多选模式
          btn.innerHTML = '<i class="fa-solid fa-download"></i> 下载';
          btn.onclick = downloadSelectedIllusts;

          // 添加取消按钮
          const cancelBtn = document.createElement('button');
          cancelBtn.id = 'cancelBtn';
          cancelBtn.className = 'multi-select-btn cancel-btn rd-semi';
          cancelBtn.innerHTML = '<i class="fa-solid fa-times mgr-2-5"></i> 取消';
          cancelBtn.onclick = exitMultiSelectMode;
          controls.appendChild(cancelBtn);

          // 显示选择框
          overlays.forEach(overlay => {
            overlay.style.display = 'flex';
          });

          // 绑定点击事件
          bindMultiSelectEvents();

          notyf.success('已进入多选模式，最多可选择50张图片');
        } else {
          exitMultiSelectMode();
        }
      }

      // 退出多选模式
      function exitMultiSelectMode() {
        isMultiSelectMode = false;
        selectedIllusts.clear();

        const btn = document.getElementById('multiSelectBtn');
        const cancelBtn = document.getElementById('cancelBtn');
        const overlays = document.querySelectorAll('.selection-overlay');

        // 恢复按钮状态
        btn.innerHTML = '<i class="fa-solid fa-check-square"></i> 多选';
        btn.onclick = toggleMultiSelectMode;

        // 移除取消按钮
        if (cancelBtn) {
          cancelBtn.remove();
        }

        // 隐藏选择框并重置状态
        overlays.forEach(overlay => {
          overlay.style.display = 'none';
          const checkbox = overlay.querySelector('.selection-checkbox i');
          checkbox.className = 'fa-solid fa-plus';
          const container = overlay.closest('.thumb-container');
          container.classList.remove('selected');
        });

        // 解绑多选事件
        unbindMultiSelectEvents();
      }

      // 绑定多选模式的事件
      function bindMultiSelectEvents() {
        const thumbContainers = document.querySelectorAll('.thumb-container:not([data-multi-select-bound])');

        thumbContainers.forEach(container => {
          const link = container.querySelector('.illust-link');
          const overlay = container.querySelector('.selection-overlay');

          // 阻止原有的链接跳转
          link.addEventListener('click', handleMultiSelectClick);

          // 点击选择框也能选中
          overlay.addEventListener('click', handleMultiSelectClick);

          // 标记已绑定事件
          container.setAttribute('data-multi-select-bound', 'true');
        });
      }

      // 解绑多选模式的事件
      function unbindMultiSelectEvents() {
        const thumbContainers = document.querySelectorAll('.thumb-container[data-multi-select-bound]');

        thumbContainers.forEach(container => {
          const link = container.querySelector('.illust-link');
          const overlay = container.querySelector('.selection-overlay');

          link.removeEventListener('click', handleMultiSelectClick);
          overlay.removeEventListener('click', handleMultiSelectClick);

          // 移除绑定标记
          container.removeAttribute('data-multi-select-bound');
        });
      }

      // 处理多选模式下的点击事件
      function handleMultiSelectClick(event) {
        if (!isMultiSelectMode) return;

        event.preventDefault();
        event.stopPropagation();

        const container = event.target.closest('.thumb-container');
        const illustId = container.dataset.illustId;
        const checkbox = container.querySelector('.selection-checkbox i');

        if (selectedIllusts.has(illustId)) {
          // 取消选择
          selectedIllusts.delete(illustId);
          checkbox.className = 'fa-solid fa-plus';
          container.classList.remove('selected');
        } else {
          // 选择图片
          if (selectedIllusts.size >= maxSelection) {
            notyf.error(`最多只能选择${maxSelection}张图片`);
            return;
          }

          selectedIllusts.add(illustId);
          checkbox.className = 'fa-solid fa-check';
          container.classList.add('selected');
        }

        // 更新按钮文本显示选择数量
        const btn = document.getElementById('multiSelectBtn');
        if (selectedIllusts.size > 0) {
          btn.innerHTML = `<i class="fa-solid fa-download"></i> 下载 (${selectedIllusts.size})`;
        } else {
          btn.innerHTML = '<i class="fa-solid fa-download"></i> 下载';
        }
      }

      // 下载选中的图片
      async function downloadSelectedIllusts() {
        if (selectedIllusts.size === 0) {
          notyf.error('请先选择要下载的图片');
          return;
        }

        const iids = Array.from(selectedIllusts).join(',');

        try {
          await fetchAPI(
            'POST',
            '/a/illust/batch_download/', {
              iids: iids
            },
            async (data) => {
              if (data.rc === 0) {
                await download(data.hd_urls);
                exitMultiSelectMode();
              }
            }
          );
        } catch (error) {
          console.error('批量下载失败:', error);
          notyf.error('批量下载失败');
        }
      }

      // 处理动态加载的内容
      function handleDynamicContent() {
        if (isMultiSelectMode) {
          // 为新加载的内容显示选择框
          const newOverlays = document.querySelectorAll('.selection-overlay');
          newOverlays.forEach(overlay => {
            overlay.style.display = 'flex';
          });

          // 绑定新内容的事件
          bindMultiSelectEvents();
        }
      }

      // 监听DOM变化，处理动态加载的内容
      const observer = new MutationObserver((mutations) => {
        mutations.forEach((mutation) => {
          if (mutation.type === 'childList' && mutation.addedNodes.length > 0) {
            // 检查是否有新的图片容器被添加
            const hasNewThumbs = Array.from(mutation.addedNodes).some(node =>
              node.nodeType === Node.ELEMENT_NODE &&
              (node.classList.contains('thumb-container') || node.querySelector('.thumb-container'))
            );

            if (hasNewThumbs) {
              handleDynamicContent();
            }
          }
        });
      });

      // 开始观察DOM变化
      observer.observe(document.body, {
        childList: true,
        subtree: true
      });
    </script>

    <!-- 动态加载 -->
    <script type="text/javascript"
            src="{{ static('js/functions.js') }}"></script>
    <script type="text/javascript">lazyLoadNextPage('illustsContainer');</script>
  {% endif %}
{% endblock %}
