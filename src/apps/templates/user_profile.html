{% extends 'base.html' %}

{% block left_side %}
  <div class="content pure-u-1 pure-u-md-4-5 pd-1 h-100">
    <h1>
      <i class="fa-solid fa-address-card mgr-2-5 fg-blue"></i>{{ user.name }} 的资料
    </h1>
    <hr />

    <form class="pure-form pure-form-aligned">
      <fieldset>
        <div class="pure-control-group fg-emp">
          <label>
            <i class="fa-regular fa-address-card"></i>
          </label>
          <strong>基本信息</strong>
        </div>
        <!-- 邮箱 （不可修改） -->
        <div class="pure-control-group">
          <label>
            <i class="fa-regular fa-envelope fg-blue"></i>
          </label>
          <span class="b-900">{{ user.email }}</span>
        </div>
        <!-- 用户金币 -->
        <div class="pure-control-group">
          <label>
            <i class="fa-solid fa-coins fg-golden"></i>
          </label>
          <span>{{ user.coins }} 次元币</span>
          <a class="fg-rose underline xs mgx-2-5"
             href="/w/user/ad/award/"
             target="_blank">看广告赚金币
            <i class="fa-solid fa-gift fa-beat"></i>
          </a>
        </div>
        <!-- VIP状态 -->
        {% if user.vip.level > 0 %}
          <div class="pure-control-group">
            <label>
              <i class="fa-solid fa-crown fg-golden"></i>
            </label>
            <span class="fg-white bg-dazzling pdh-1 rd-5 sm"><strong>{{ user.vip.name }}</strong></span>
          </div>
          <div class="pure-control-group">
            <label>
              <i class="fa-solid fa-clock-rotate-left"></i>
            </label>
            <span>将于 {{ user.vend.date() }} 到期</span>
          </div>
        {% else %}
          <div class="pure-control-group">
            <label>
              <i class="fa-solid fa-crown fg-golden"></i>
            </label>
            <span>非会员</span>
          </div>
        {% endif %}
      </fieldset>
    </form>

    <form class="pure-form pure-form-aligned">
      <fieldset>
        <div class="pure-control-group fg-emp">
          <label>
            <i class="fa-solid fa-check-to-slot fg-rose"></i>
          </label>
          <strong class="mgr-3-5 t-vcenter">本周累计打卡 {{ n_checked }} 次</strong>
          <button class="pure-button xs rd-semi fg-white bg-blue pdh-1"
                  onclick="javascript:fetchAPI('GET', '/w/user/checkin/')"
                  type="button">立即打卡</button>
        </div>
        <div class="pure-control-group">
          <label>
            <i class="fa-solid fa-calendar-days fg-blue"></i>
          </label>
          {% for wd, checked in checkin_log %}
            <span class="
                         {% if checked %}
                           fg-{{ loop.cycle('red', 'orange', 'golden', 'lightgreen', 'cyan', 'blue', 'purple') }}
                         {% else %}
                           fg-lightgray
                         {% endif %}
                         mgr-3-5">{{ wd }}</span>
          {% endfor %}
        </div>
        <div class="pure-control-group">
          <label>
            <i class="fa-solid fa-flag-checkered fg-red"></i>
          </label>
          <a class="fg-blue underline" href="/w/purchase/#checkinNote">连续打卡，奖励多多！<i class="fa-solid fa-circle-info"></i></a>
        </div>

      </fieldset>
    </form>

    <form id="profileForm" class="pure-form pure-form-aligned">
      <fieldset>
        <div class="pure-control-group fg-emp">
          <label>
            <i class="fa-solid fa-pen-to-square"></i>
          </label>
          <strong>修改密码</strong>
        </div>

        <!-- 新密码 -->
        <div class="pure-control-group">
          <label for="passwordInput">
            <i class="fa-solid fa-key fg-blue"></i>
          </label>
          <input id="passwordInput"
                 type="password"
                 name="password"
                 placeholder="输入新密码" />
        </div>

        <!-- 确认密码 -->
        <div class="pure-control-group">
          <label for="confirmInput">
            <i class="fa-solid fa-check-double fg-blue"></i>
          </label>
          <input id="confirmInput"
                 type="password"
                 name="confirm"
                 placeholder="请再次输入" />
        </div>

        <!-- 提交 -->
        <div class="pure-control-group">
          <label for="saveButton"></label>
          <button type="submit" class="pure-button fg-white bg-blue rd-semi">保存</button>
        </div>
      </fieldset>
    </form>
  </div>
{% endblock %}

{% block right_side %}
  <div class="pure-u-1 pure-u-md-1-5">{% include 'u_sidebar.html' %}</div>
{% endblock %}

{% block extra_js %}
  <script type="text/javascript">
    const passwordInput = document.getElementById('passwordInput');
    const confirmInput = document.getElementById('confirmInput');
    var pwdMinLength = +"{{ cfg.PWD_MIN_LEN }}";

    // 焦点离开时，清除密码输入框的验证信息
    passwordInput.addEventListener('blur', () => passwordInput.setCustomValidity(''));
    confirmInput.addEventListener('blur', () => confirmInput.setCustomValidity(''));

    // 修改个人资料
    document.getElementById('profileForm').addEventListener("submit", async function(event) {
      event.preventDefault();

      // 检查密码是否正确输入
      if (!chkPassword(passwordInput, +"{{ cfg.PWD_MIN_LEN }}")) return;
      if (!chkConfirm(passwordInput, confirmInput)) return;

      await fetchAPI(
        'POST',
        '/w/user/profile/', {
          password: passwordInput.value.trim()
        }
      );
    });
  </script>
{% endblock %}
