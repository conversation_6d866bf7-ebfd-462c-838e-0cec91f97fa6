{% extends 'base.html' %}

<!-- 内容区 -->
{% block left_side %}
  <div class="container">
    <div class="panel mg-center pd-2">
      <!-- 标题 -->
      <div class="pure-menu pure-menu-horizontal t-center">
        <ul class="pure-menu-list tab-menu">
          <li class="pure-menu-item pure-menu-selected">
            <a href="#" class="pure-menu-link lg b-700">
              <i class="fa-solid fa-key mgr-2-5"></i>重置密码
            </a>
          </li>
        </ul>
      </div>

      <!-- 重置密码表单 -->
      <div id="resetPasswordFormContainer">
        <form id="resetPasswordForm"
              method="post"
              action="/w/user/reset_password/"
              class="pure-form pure-form-aligned">
          <fieldset>
            <div class="pure-control-group">
              <label for="resetEmail">
                <i class="fa-regular fa-envelope"></i>
              </label>
              <input type="email"
                     id="resetEmail"
                     name="email"
                     placeholder="请输入注册邮箱"
                     required />
            </div>

            <div class="pure-control-group">
              <label for="resetVcode">
                <i class="fa-solid fa-shield-halved"></i>
              </label>
              <input type="text"
                     id="resetVcode"
                     name="vcode"
                     placeholder="请输入验证码"
                     required />
            </div>
            <div class="pure-control-group">
              <label for="vcodeButton"></label>
              <button type="button"
                      id="sendVcodeBtn"
                      class="pure-button rd-semi xs fg-white bg-blue">发送验证码</button>
            </div>

            <div class="pure-control-group">
              <label for="resetPassword">
                <i class="fa-solid fa-key"></i>
              </label>
              <input type="password"
                     id="resetPassword"
                     name="password"
                     placeholder="请输入新密码"
                     required />
            </div>

            <div class="pure-control-group">
              <label for="resetConfirm">
                <i class="fa-solid fa-check-double"></i>
              </label>
              <input type="password"
                     id="resetConfirm"
                     name="confirm"
                     placeholder="请再次输入新密码"
                     required />
              <br />
              <label for="passwordStrength"></label>
              <span class="xs fg-orange">密码长度不能少于 {{ cfg.PWD_MIN_LEN }} 个字符</span>
              <br />
              <label for="passwordStrength"></label>
              <span class="xs fg-orange">且必须包含大写字母和数字</span>
            </div>

            <div class="pure-control-group">
              <label for="resetButton"></label>
              <button type="submit"
                      class="pure-button fg-white bg-blue rd-semi b-500">
                <i class="fa-solid fa-check mgr-2-5"></i>重置密码
              </button>
            </div>
          </fieldset>
        </form>
      </div>
    </div>
  </div>
{% endblock %}

{% block extra_js %}
  <script type="text/javascript">
    const emailInput = document.getElementById('resetEmail');
    const sendButton = document.getElementById('sendVcodeBtn');

    // 发送验证码
    sendButton.addEventListener("click", async function(event) {
      // 检查 Cookie 中是否有上次发送验证码的时间戳
      var nextSendTime = getCookie("nextSendTime");
      var currentTime = new Date().getTime();
      if (nextSendTime && currentTime < nextSendTime) {
        var remainingTime = Math.round((nextSendTime - currentTime) / 1000);
        notyf.error("请等待 " + remainingTime + " 秒后再发送验证码");
        return;
      }

      if (!emailValidation(emailInput)) return; // 检查邮箱是否输入正确

      // 调用发送验证码接口
      await fetchAPI(
        'GET',
        '/a/reset/vcode/', {
          email: emailInput.value.trim()
        },
        (data) => {
          // 禁用按钮
          sendButton.disabled = true;

          // 设置倒计时 120 秒
          var countdown = 120;
          var countdownInterval = setInterval(function() {
            if (countdown <= 0) {
              clearInterval(countdownInterval);
              sendButton.disabled = false;
              sendButton.textContent = '发送验证码';
            } else {
              sendButton.textContent = '重新发送 (' + countdown + ')';
              countdown--;
            }
          }, 1000);

          // 在 Cookie 中记录下次发送验证码的时间戳
          var nextSendTime = new Date().getTime() + 120 * 1000;
          document.cookie = 'nextSendTime=' + nextSendTime + '; path=/';
        }
      );
    });

    const vcodeInput = document.getElementById('resetVcode');
    const passwordInput = document.getElementById("resetPassword");
    const confirmInput = document.getElementById("resetConfirm");

    // 焦点离开时，清除密码输入框的验证信息
    passwordInput.addEventListener('blur', () => passwordInput.setCustomValidity(''));
    confirmInput.addEventListener('blur', () => confirmInput.setCustomValidity(''));

    // 重置密码表单提交
    document.getElementById("resetPasswordForm").addEventListener("submit", async function(event) {
      event.preventDefault();

      // 检查密码是否正确输入
      if (!chkPassword(passwordInput, +"{{ cfg.PWD_MIN_LEN }}")) return;
      if (!chkConfirm(passwordInput, confirmInput)) return;

      await fetchAPI(
        'POST',
        this.action, {
          email: emailInput.value.trim(),
          password: passwordInput.value.trim(),
          confirm: confirmInput.value.trim(),
          vcode: vcodeInput.value.trim()
        },
        (data) => {
            if (data.rc === 0) {
                setTimeout(() => {
                    window.location.href = '/w/login/';
                }, 2000);
            }
        }
      );
    });
  </script>
{% endblock %}
