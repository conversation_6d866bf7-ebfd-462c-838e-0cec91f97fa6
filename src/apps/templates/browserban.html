<!DOCTYPE html>
<html lang="zh-CN">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport"
          content="width=device-width, initial-scale=1.0" />
    <title>次元画册</title>
    <meta name="description" content="请在系统浏览器中打开此页面以获得最佳体验。" />
    <meta name="keywords" content="系统浏览器, 打开, 链接" />
    <style>
      body {
        margin: 0;
        padding: 20px;
        background-color: #f5f5f5;
        color: #555;
        font-size: 16px;
        line-height: 1.6;
      }

      h1 {
        color: #333;
        font-size: 21px;
        font-weight: bold;
        text-align: center;
        margin-bottom: 20px;
      }

      .container {
        max-width: 600px;
        margin: 20px auto;
        padding: 20px;
        background: #fff;
        border-radius: 12px;
        box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
      }

      .icon {
        text-align: center;
        font-size: 2.5em;
        margin-bottom: 10px;
      }

      .copy-btn {
        display: block;
        width: 100%;
        padding: 12px;
        background: #007bff;
        color: #fff;
        border: none;
        border-radius: 6px;
        font-size: 16px;
        cursor: pointer;
        margin-bottom: 15px;
      }

      .copy-btn:active {
        background: #0056b3;
      }

      .tips {
        color: black;
        font-weight: 900;
      }
    </style>
  </head>
  <body>
    <div class="container">
      <div class="icon">🌐</div>
      <h1>请在系统浏览器中打开</h1>
      <p>为了获得最佳体验，请在系统浏览器中打开当前页面。</p>
      <p>
        方法一：
        <ol>
          <li>
            点击右上角的 "<span class="tips">•••</span>" 按钮。
          </li>
          <li>
            点击"<span class="tips">在浏览器中打开</span>"，即可在系统浏览器中查看当前页面。
          </li>
        </ol>
      </p>
      <p>
        方法二：
        <ol>
          <li>
            点击下方的 "<span class="tips">复制链接</span>" 按钮。
          </li>
          <li>手动打开系统浏览器。</li>
          <li>
            把复制的链接<span class="tips">粘贴到地址栏</span>，然后打开。
          </li>
        </ol>
      </p>
      <button class="copy-btn" onclick="copyUrl()">复制链接</button>
    </div>

    <script>
      // 获取当前URL并显示
      var currentUrl = '';
      window.onload = function() {
        currentUrl = window.location.href;
      };

      // 复制URL功能
      function copyUrl() {
        var urlText = currentUrl;

        // 创建临时输入框
        var tempInput = document.createElement('input');
        tempInput.style.position = 'absolute';
        tempInput.style.left = '-9999px';
        tempInput.value = urlText;
        document.body.appendChild(tempInput);

        // 选择并复制
        tempInput.select();
        document.execCommand('copy');
        document.body.removeChild(tempInput);

        // 更新按钮文字提示复制成功
        var btn = document.querySelector('.copy-btn');
        var originalText = btn.textContent;
        btn.textContent = '复制成功！';
        btn.style.background = '#28a745';

        setTimeout(function() {
          btn.textContent = originalText;
          btn.style.background = '#007bff';
        }, 2000);
      }
    </script>
  </body>
</html>
