{% extends 'base.html' %}

{% block left_side %}
  <h1 class="pure-u-1">购买会员</h1>
  <p class="mgb-1">欢迎亲爱的小伙伴们加入「次元画册」会员大家庭。成为会员能享受更多的权限，下载资源更加划算。</p>
  <hr />
  <div class="pure-menu pure-menu-horizontal t-center">
    <ul class="pure-menu-list tab-menu">
      <li class="pure-menu-item">
        <a id="vipTab" href="#vipTab" class="pure-menu-link xl b-700">
          <i class="fa-solid fa-crown mgr-2-5"></i>会员卡
        </a>
      </li>
      <li class="pure-menu-item">
        <a id="coinTab"
           href="#coinTab"
           class="pure-menu-link xl b-700">
          <i class="fa-solid fa-coins mgr-2-5"></i>次元币
        </a>
      </li>
    </ul>
  </div>

  <!-- 会员卡 -->
  <div id="vipContainer" class="pure-g w-100">
    {% for vip in pur_vips %}
      <div class="pure-u-1
                  {% if n_vip >= 3 %}pure-u-sm-1-3{% endif %}
                  {% if n_vip >= 4 %}pure-u-md-1-4{% endif %}
                  {% if n_vip >= 5 %}pure-u-lg-1-5{% endif %}">
        <div class="mg-1-5">
          <div class="grid-container pd-1 mg-0
                      {% if loop.last %}gbg-golden{% endif %}">
            <!-- 标题 -->
            <h3 class="t-center mg-0 {% if loop.last %}fg-brown{% endif %}">
              <i class="fa-solid fa-crown mgr-2-5"></i>{{ vip.name }}
            </h3>
            <hr />
            <div class="t-left sm">
              <!-- VIP 特权 -->
              <ul class="fa-ul">
                {% for lv, desc, enable in cfg.PERMISSIONS.values() %}
                  {% if enable %}
                    {% if vip.level < lv %}
                      <li class="empty">&emsp;</li>
                    {% elif vip.level == lv %}
                      <li class="new">{{ desc |safe }}</li>
                    {% else %}
                      <li>{{ desc |safe }}</li>
                    {% endif %}
                  {% endif %}
                {% endfor %}
              </ul>

              <!-- 时长 -->
              <div class="mg-1-5 b-700 pd-1-5">
                <i class="fa-solid fa-clock-rotate-left mgr-2-5"></i>时长：
                {% if loop.last %}
                  <span class="fg-brown b-900">
                    {% if vip.duration >= 36500 %}
                      永久
                    {% else %}
                      {{ vip.duration }} 天
                    {% endif %}
                  </span>
                {% else %}
                  <span class="fg-emp b-900">{{ vip.duration }} 天</span>
                {% endif %}
              </div>

              <!-- 价格 -->
              <div class="mg-1-5 b-700 pd-1-5">
                <i class="fa-solid fa-sack-dollar mgr-2-5"></i>价格：
                {% if loop.last %}
                  <span class="fg-brown b-900 ">{{ vip.price }} 元</span>
                  {% if vip.original > 0 %}<span class="emp-del">{{ vip.original }} 元</span>{% endif %}
                {% else %}
                  <span class="fg-emp b-900">{{ vip.price }} 元</span>
                  {% if vip.original > 0 %}<span class="emp-del">{{ vip.original }} 元</span>{% endif %}
                {% endif %}
              </div>
              <hr />

              <!-- 购买 -->
              {% if vip.level > 0 %}
                <button class="pure-button pure-button-primary w-100 rd-5 reflect gbg-{%- if loop.last -%} black {%- else -%} gbg-blue {%- endif -%}"
                        onclick="showPaymentDialog('/w/purchase/vip/{{ vip.id }}/')">购买</button>
              {% else %}
                <button class="pure-button w-100 rd-5" disabled>购买</button>
              {% endif %}
            </div>
          </div>
        </div>
      </div>
    {% endfor %}
  </div>

  <!-- 充值次元币 -->
  <div id="coinContainer"
       class="t-center w-100
              {% if tab != 'coin' %}hidden{% endif %}">
    <div class="pure-g">
      {% for coin in pur_coins %}
        <div class="pure-u-1 pure-u-sm-1-3">
          <div class="mg-1-5 ">
            <div class="grid-container pd-1 mg-0">
              <div>
                <h3 class="mg-0">{{ coin.name }}</h3>
                <p>{{ coin.description | safe }}</p>
                <p class="fg-emp b-700">
                  <i class="fa-solid fa-sack-dollar mgr-2-5"></i>
                  价格：<span class="b-900">{{ coin.price }}</span> 元
                </p>
              </div>
              <button class="pure-button pure-button-primary w-100 bg-blue rd-semi reflect"
                      onclick="showPaymentDialog('/w/purchase/coin/{{ coin.id }}')">充值</button>
            </div>
          </div>
        </div>
      {% endfor %}
    </div>
  </div>

  <div class="w-100 pd-1">
    <a href="#statement">
      <h2 id="statement">重要声明</h2>
    </a>
    <ol class="gbg-blue rd-15 pdh-3 line-2">
      <li>本站所有内容均来自于网络上的公开资源，版权归原作者所有。</li>
      <li>所有资源仅限于小范围交流学习使用，并请在下载后24小时内删除。</li>
      <li>本站不含任何 R18 内容，寻找此类内容的访客请绕道。</li>
      <li>未成年人请勿充值。</li>
    </ol>

    <a href="#purchaseNote">
      <h2 id="purchaseNote">购买须知</h2>
    </a>
    <p>
      <span class="emp-blue">「二次元专辑」</span>是按作品、角色或主题精心整理的插画集。
    </p>
    <p>
      <span class="emp-blue">「Coser 合集」</span>是单个 Coser 的所有写真套图的合集。
    </p>
    <p>
      <span class="emp-blue">「二次元图库」</span>是本站全部二次元插画的超大合集，如今已达到<span class="emp-blue">10 万多张</span>美图，并且每月都会更新。
    </p>
    <p>
      <span class="emp-blue">「Cosplay图库」</span>目前已包含<span class="emp-blue">数千套 Cosplay 套图</span>，不定期更新。
    </p>
    <p>会员卡、次元币等项目所收取的费用仅用于维持本站服务器的运行。</p>
    <p>会员卡和次元币均为虚拟物品，购买后不支持退款。</p>
    <p>如果当前您已经是 VIP 用户，想购买更高级会员卡时，当前的剩余天数会按价格折算为高级会员的天数。</p>

    <a href="#checkinNote">
      <h2 id="checkinNote">打卡奖励</h2>
    </a>
    <p>每天签到打卡都可以免费领取次元币，每天的奖励各不相同，累计越多奖励越多。只要连续打卡，所有资源都能够免费下载。</p>
    <p>
      一周连续七天打卡一共可获得次元币<span class="emp-blue">{{ sum(cfg.CHECKIN_AWARD.values() ) }} 枚</span>，可用于免费下载<span class="emp-blue">{{ round(sum(cfg.CHECKIN_AWARD.values() ) / cfg.PRICE_PER_ILLUST) }} 张</span>美图壁纸。
    </p>
    <p>打卡次数会在每周一的凌晨零点进行重置，所以每周只有连续七天打卡才能获得最高的奖励哦。</p>
    <p class="b-500">详情如下所示：</p>
    <ul>
      {% for day, coins in cfg.CHECKIN_AWARD.items() %}
        <li class="mono">
          {{ day }}：<span class="fg-{{ loop.cycle('blue', 'rose', 'blue', 'blue', 'blue', 'rose', 'red') }} mono-3
             {% if loop.last %}b-700{% endif %}">{{ coins }} 枚</span>
        </li>
      {% endfor %}
    </ul>
    <a href="#qualityNote">
      <h2 id="qualityNote">资源质量</h2>
    </a>
    <p>本站内容并不算最全，但我们会努力做到最精！</p>
    <p>无论是二次元插画，还是 Cosplay 套图，每一张发布出来的作品都是经过了精挑细选。</p>
    <p>二次元插画主图均达到了 4K 甚至 8K 画质，Cosplay 大部分照片的大小都达到了几十MB。</p>
    <p>
      并且<span class="emp-pink">绝无水印</span>、<span class="emp-pink">绝不剪裁</span>、<span class="emp-pink">绝对高清</span>。
    </p>
    <p>
      随着本站收录的资源逐渐增多，各级 VIP 的价值也会持续升高，未来价格一定会上涨，所以<span class="emp-blue">早上车早享受</span>！
    </p>
  </div>

  <!-- 支付方式选择对话框 -->
  <div id="paymentDialog" class="payment-dialog hidden">
    <div class="panel pd-2">
      <h3 class="mg-0 t-center">选择支付方式</h3>
      <hr />
      <form class="pure-form pure-form-stacked">
        <div class="center">
          <label for="aliPay" class="pure-radio ptype-wrapper">
            <input id="aliPay"
                   type="radio"
                   name="ptype"
                   value="alipay"
                   class="mgr-2-5" />
            <i class="fa-brands fa-alipay fg-blue mgr-2-5"></i>支付宝
          </label>
        </div>
        <div class="center">
          <label for="wxPay" class="pure-radio ptype-wrapper">
            <input id="wxPay"
                   type="radio"
                   name="ptype"
                   value="wxpay"
                   class="mgr-1-5" />
            <i class="fa-brands fa-weixin fg-wxgreen mgr-2-5"></i>微信支付
          </label>
        </div>
        <!-- <div class="center">
                    <label for="qqPay" class="pure-radio ptype-wrapper">
                        <input id="qqPay" type="radio" name="ptype" value="qqpay" class="mgr-2-5" />
                        <i class="fa-brands fa-qq fg-black mgr-2-5"></i>QQ 支付
                    </label>
                </div> -->
      </form>
      <hr />
      <div class="t-center">
        <button class="pure-button rd-5 fg-white bg-blue mgr-1"
                onclick="submitPayment()">提交订单</button>
        <button class="pure-button rd-5" onclick="hidePaymentDialog()">取消</button>
      </div>
    </div>
  </div>

  <div id="wxpayDialog" class="payment-dialog hidden">
    <div class="panel-w pd-2 relative">
      <i class="far fa-times-circle close-btn"
         onclick="hidePaymentDialog()"></i>
      <h3 class="mg-0 t-center">微信暂时只支持赞赏码支付</h3>
      <hr />
      <div class="pure-g">
        <div class="pure-u-1 pure-u-md-1-2 pd-2-5">
          <img src="{{ static('img', 'reward_code.jpg') }}"
               alt="微信赞赏码"
               class="payment-code shadow mg-center" />
          <p class="t-center">请用微信扫二维码进行支付</p>
        </div>
        <div class="pure-u-1 pure-u-md-1-2 pd-2-5">
          <h5 class="t-center fg-red">备注：请务必填写您的邮箱地址</h5>
          <img src="{{ static('img', 'reward_confirm.jpg') }}"
               alt="微信赞赏提示"
               class="payment-code mg-center" />
        </div>
      </div>
    </div>
  </div>
{% endblock %}

{% block extra_js %}
  <script type="text/javascript">
    document.addEventListener('DOMContentLoaded', () => {
      const vipContainer = document.getElementById('vipContainer');
      const coinContainer = document.getElementById('coinContainer');
      const vipTab = document.getElementById('vipTab').parentElement;
      const coinTab = document.getElementById('coinTab').parentElement;

      function switchTab(hash) {
        if (hash === '#coinTab') {
          vipContainer.classList.add('hidden');
          coinContainer.classList.remove('hidden');
          vipTab.classList.remove('pure-menu-selected');
          coinTab.classList.add('pure-menu-selected');
        } else {
          vipContainer.classList.remove('hidden');
          coinContainer.classList.add('hidden');
          vipTab.classList.add('pure-menu-selected');
          coinTab.classList.remove('pure-menu-selected');
        }
      }

      // 初始化，根据 URL 锚点值切换
      switchTab(window.location.hash);

      // 添加点击事件监听
      document.querySelectorAll('.pure-menu-link').forEach(link => {
        link.addEventListener('click', (e) => {
          switchTab(e.currentTarget.getAttribute('href'));
        });
      });

      // 监听 URL 锚点变化
      window.addEventListener('hashchange', () => {
        switchTab(window.location.hash);
      });
    });
  </script>
  <script type="text/javascript">
    let currentPaymentUrl = '';

    function showPaymentDialog(baseUrl) {
      currentPaymentUrl = baseUrl;
      document.getElementById('paymentDialog').classList.remove('hidden');
    }

    function showWxpayDialog() {
      document.getElementById('wxpayDialog').classList.remove('hidden');
    }

    function hidePaymentDialog() {
      currentPaymentUrl = '';
      document.getElementById('paymentDialog').classList.add('hidden');
      document.getElementById('wxpayDialog').classList.add('hidden');
    }

    function submitPayment() {
      const selectedPayment = document.querySelector('input[name="ptype"]:checked');
      if (!selectedPayment) {
        notyf.error('请先选择支付方式');
        return;
      }
      if (selectedPayment.value === 'wxpay') {
        showWxpayDialog();
        return;
      }

      const finalUrl = `${currentPaymentUrl}?ptype=${selectedPayment.value}`;

      window.location.href = finalUrl;
    }
  </script>
{% endblock %}
