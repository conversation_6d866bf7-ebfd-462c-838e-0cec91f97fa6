{% extends 'base.html' %}

{% block left_side %}

  <div class="pure-menu pure-menu-horizontal t-center">
    <!-- 标签页菜单 -->
    <div id="privacyTab"
         class="pure-menu pure-menu-horizontal t-center">
      <ul class="pure-menu-list tab-menu">
        <li class="pure-menu-item pure-menu-selected">
          <a href="#zh" class="pure-menu-link lg b-700" data-tab="zh">隐私政策</a>
        </li>
        <li class="pure-menu-item">
          <a href="#en" class="pure-menu-link lg b-700" data-tab="en">Privacy Policy</a>
        </li>
        <li class="pure-menu-item">
          <a href="#copyright"
             class="pure-menu-link lg b-700"
             data-tab="copyright">版权声明</a>
        </li>
      </ul>
    </div>
  </div>

  <!-- 中文内容 -->
  <div id="zhContainer" class="w-70 mg-center">
    <p>我们非常重视您的隐私。“次元画册”（以下简称“本站”）仅在确保用户安全和正常使用功能的前提下，使用极少量的信息。</p>

    <p>请您仔细阅读以下隐私政策：</p>

    <h2>1. 信息收集情况</h2>

    <p>本站不会主动收集或存储用户的个人隐私信息，例如姓名、邮箱、电话号码、地理位置等。</p>

    <h2>2. Cookie 使用说明</h2>

    <p>为了实现基本功能，例如登录状态的维持，本站会使用 Cookie 技术。具体情况如下：</p>

    <p>Cookie 仅用于识别登录状态，不包含任何个人身份信息。</p>

    <p>您可以在浏览器设置中清除或禁用 Cookie，但可能会导致登录状态失效或部分功能无法使用。</p>

    <h2>3. 第三方服务</h2>

    <p>本站会集成一些第三方广告、统计分析、追踪或社交平台插件，但不会共享您的任何隐私信息。</p>

    <p>详见页底。</p>

    <h2>4. 数据安全</h2>

    <p>由于本站不存储用户隐私信息，因此不存在隐私数据泄露的风险。登录状态相关数据仅限于服务器端会话验证，不进行长期存储或分析。</p>

    <h2>5. 联系我们</h2>

    <p>如您对本隐私政策有任何疑问或担忧，请通过以下方式联系我们：</p>

    <p>邮箱：{{ cfg.ADMIN_EMAIL }}</p>

    <h2>6. 政策更新</h2>

    <p>我们可能会根据法律或服务变化，适时更新本政策。更新后的版本将在本页面上公布，并即时生效。建议用户定期查阅本政策。</p>
  </div>

  <!-- 英文内容 -->
  <div id="enContainer" class="w-70 mg-center hidden">
    <p>
      We take your privacy seriously. "Ci Yuan Hua Ce" (hereinafter referred to as "the Site") is committed to protecting the privacy of its users.
    </p>

    <p>Please read the following policy carefully.</p>

    <h2>1. Information Collection</h2>

    <p>
      The Site does not actively collect or store any personal information such as your name, email address, phone number, or location.
    </p>

    <h2>2. Use of Cookies</h2>

    <p>
      To enable essential functionality—specifically, to maintain user login status—the Site uses cookies. Details are as follows:
    </p>

    <p>Cookies are used solely for identifying login sessions.</p>

    <p>Cookies do not contain any personally identifiable information.</p>

    <p>
      You may disable or delete cookies via your browser settings, but doing so may result in logout or limited functionality.
    </p>

    <h2>3. Third-Party Services</h2>

    <p>
      The Site will integrate some third-party advertising, statistical analysis, tracking or social platform plug-ins, but will not share any of your private information.
    </p>

    <p>For details, please refer to the bottom of the page.</p>

    <h2>4. Data Security</h2>

    <p>
      Since no personal data is collected or stored, there is no risk of personal data leakage. Session-related data is used strictly for authentication purposes and is not retained long-term.
    </p>

    <h2>5. Contact</h2>

    <p>If you have any questions or concerns about this Privacy Policy, please contact us at:</p>

    <p>Email: {{ cfg.ADMIN_EMAIL }}</p>

    <h2>6. Policy Updates</h2>

    <p>
      We may update this policy periodically to reflect changes in regulations or services. Updated versions will be posted on this page and take effect immediately. Users are encouraged to review this policy regularly.
    </p>
  </div>

  <!-- 版权声明 -->
  <div id="copyrightContainer" class="w-70 mg-center hidden">
    <h2>版权声明 (中文)</h2>
    <p>本站所有图片资源版权归原作者所有，仅限于小范围内传播、学习使用，并请在下载后24小时内删除。禁止一切商用行为。如果有侵权请联系管理员删除：<EMAIL>，敬请谅解!。</p>
    <hr />
    <h2>Copyright Notice (English)</h2>
    <p>
      All image resources on this site are copyrighted by their original authors. They are intended for limited circulation and educational use only. Please delete them within 24 hours of downloading. All commercial use is strictly prohibited. If there is any infringement, please contact the administrator to delete it: <EMAIL>. We appreciate your understanding.
    </p>
  </div>

  <div class="w-70 mg-center">
    <span id="ezoic-privacy-policy-embed"></span>
  </div>

{% endblock %}

{% block extra_js %}
  <script type="text/javascript">
    document.addEventListener('DOMContentLoaded', function() {
      const tabLinks = document.querySelectorAll('#privacyTab .pure-menu-link');
      const tabContents = {
        zh: document.getElementById('zhContainer'),
        en: document.getElementById('enContainer'),
        copyright: document.getElementById('copyrightContainer')
      };
      const tabs = {
        zh: document.querySelector('[data-tab="zh"]').parentElement,
        en: document.querySelector('[data-tab="en"]').parentElement,
        copyright: document.querySelector('[data-tab="copyright"]').parentElement
      };

      function switchTab(targetTab) {
        // Hide all content
        Object.values(tabContents).forEach(content => content.classList.add('hidden'));
        // Deactivate all tabs
        Object.values(tabs).forEach(tab => tab.classList.remove('pure-menu-selected'));

        // Show target content
        if (tabContents[targetTab]) {
          tabContents[targetTab].classList.remove('hidden');
        }
        // Activate target tab
        if (tabs[targetTab]) {
          tabs[targetTab].classList.add('pure-menu-selected');
        }
      }

      // Handle tab clicks
      tabLinks.forEach(link => {
        link.addEventListener('click', (e) => {
          e.preventDefault();
          const targetTab = e.currentTarget.getAttribute('data-tab');
          switchTab(targetTab);
          // Update URL hash without jumping
          history.pushState(null, null, '#' + targetTab);
        });
      });

      // Handle initial load based on URL hash
      const currentHash = window.location.hash;
      if (currentHash === '#en') {
        switchTab('en');
      } else if (currentHash === '#copyright') {
        switchTab('copyright');
      } else {
        switchTab('zh');
      }

      // Handle back/forward button navigation
      window.addEventListener('popstate', function() {
        const hash = window.location.hash;
        if (hash === '#en') {
          switchTab('en');
        } else if (hash === '#copyright') {
          switchTab('copyright');
        } else {
          switchTab('zh');
        }
      });
    });
  </script>
{% endblock %}
