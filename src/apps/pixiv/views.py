from random import sample
from urllib.parse import urlencode

from fastapi import Request
from fastapi.responses import HTMLResponse

from apps.album import apis as a_api
from apps.pixiv import apis as p_api
from common.decorator import page_hits
from common.notice import get_notice
from libs.http import redirect, render


async def home():
    """首页"""
    num = 12
    tags, _ = await p_api.tag_rcmd()  # 获取热门标签
    result = {
        'albums': (await a_api.album_rcmd())[:num],  # 获取专辑推荐
        'tags': tags[: num + 9],  # 获取热门标签
        'artists': (await p_api.artist_rcmd())[:num],  # 获取热门画师
        'illusts': (await p_api.illust_rcmd())[0],  # 获取推荐插画
        'notices': await get_notice(),  # 获取公告
    }
    return render('apps/home.html', **result)


@page_hits('search', 'kw', 'stype')
async def search(
    request: Request,
    kw: str,
    stype: str = 'illust',
    order: str = 'added',
    page: int = 1,
    aspect: str = 'all',
    ss: str = 'all',
    sp: int = 0,
):
    """
    搜索

    order: added / created / n_view / n_bookmark
    aspect: all / ver / hor / sqr
    ss: all / 2 / 4 / 6
    """
    err = ''
    items = []
    kw = kw.strip()
    if not kw:
        return render('apps/search.html', items=[], kw=kw, err='搜索词不能为空')
    if stype == 'illust':
        match p_api.has_filter_perms(request.user, order, aspect, ss):
            case None:
                backto = urlencode({'backto': request.url})
                err = f'登录后才能使用此功能哦！<a href="/w/login/?{backto}">请点击这里</a>'
            case False:
                err = '这需要 “会员” 权限哦！<a href="/w/purchase/">查看详情</a>'
            case True:
                items, sp = await p_api.search_illust(kw, order, page, aspect, ss=ss, spos=sp)
    elif stype == 'artist':
        items = await p_api.search_artist(kw, page)
    elif stype == 'album':
        items = await a_api.search_album(kw, page)

    if page > 1:
        return render(f'apps/i_{stype}s.html', illusts=items, artists=items, albums=items, sp=sp)
    else:
        hot_words = await p_api.hot_words(stype, top_k=30)
        return render(
            'apps/search.html', items=items, kw=kw, stype=stype, order=order, err=err, sp=sp, hot_words=hot_words
        )


async def illust_rcmd(
    request: Request,
    page: int = 1,
    order: str = 'added',
    aspect: str = 'all',
    ss: str = 'all',
    sp: int = 0,
):
    """推荐插画"""
    illusts, err = [], ''
    match p_api.has_filter_perms(request.user, order, aspect, ss):  # 检查用户权限
        case None:
            backto = urlencode({'backto': request.url})
            err = f'登录后才能使用此功能哦！<a href="/w/login/?{backto}">请点击这里</a>'
        case False:
            err = '这需要 “会员” 权限哦！<a href="/w/purchase/">查看详情</a>'
        case True:
            illusts, sp = await p_api.illust_rcmd(order=order, page=page, aspect=aspect, ss=ss, spos=sp)  # 获取推荐插画

    template = 'apps/i_illusts.html' if page > 1 else 'apps/illusts.html'
    ad = False if page > 1 else True
    hot_tags = await p_api.hot_tags(top_k=60)
    hot_tags = sample(hot_tags, k=min(18, len(hot_tags)))
    return render(template, illusts=illusts, ad=ad, sp=sp, err=err, hot_tags=hot_tags)


@page_hits('illust', 'iid')
async def illust(request: Request, iid: int):
    """作品详情"""
    illust = await p_api.idetail(iid)
    if p_api.get_max_sanity() >= illust['level']:
        return render('apps/illust.html', illust=illust)
    elif request.user:
        return redirect('/w/purchase/?err=您需要升级 “会员”<br>才能查看哦！')
    else:
        return redirect(f'/w/login/?backto={request.url.path}&notice=true')


async def irelated(iid: int):
    """相关插画"""
    illusts, _ = await p_api.irelated(iid)
    return render('apps/i_illusts.html', illusts=illusts, disable_dynamic=True)


async def artist_rcmd(page: int = 1):
    """画师推荐"""
    artists = await p_api.artist_rcmd(page)
    template = 'apps/i_artists.html' if page > 1 else 'apps/artists.html'
    return render(template, artists=artists)


@page_hits('artist', 'aid')
async def artist(request: Request, aid: int, page: int = 1, sp: int = 0) -> HTMLResponse:
    """画师详情"""
    detail = await p_api.artist_detail(aid, page=page, spos=sp)
    detail['show_intro'] = True
    template = 'apps/i_illusts.html' if page > 1 else 'apps/artist.html'
    detail['ad'] = False if page > 1 else True
    return render(template, **detail)


async def tag_rcmd(page: int = 1):
    """标签推荐"""
    tags, n_tag = await p_api.tag_rcmd(page)
    return render('apps/tags.html', tags=tags, n_tag=n_tag)


@page_hits('tag', 'tid')
async def tag(
    request: Request,
    tid: int,
    page: int = 1,
    order: str = 'added',
    aspect: str = 'all',
    ss: str = 'all',
    sp: int = 0,
):
    """
    标签详情

    order: added / created / n_view / n_bookmark
    aspect: all / ver / hor / sqr
    ss: all / 2 / 4 / 6
    """
    detail, err = {'tag': '*', 'illusts': []}, ''
    match p_api.has_filter_perms(request.user, order, aspect, ss):
        case None:
            backto = urlencode({'backto': request.url})
            err = f'登录后才能使用此功能哦！<a href="/w/login/?{backto}">请点击这里</a>'
        case False:
            err = '这需要 “会员” 权限哦！<a href="/w/purchase/">查看详情</a>'
        case True:
            detail, sp = await p_api.tag_detail(tid, order, page, aspect, ss=ss, spos=sp)

    template = 'apps/i_illusts.html' if page > 1 else 'apps/tag.html'
    detail['ad'] = False if page > 1 else True  # type: ignore

    hot_tags = await p_api.hot_tags(top_k=60)
    hot_tags = sample(hot_tags, k=min(18, len(hot_tags)))
    return render(template, err=err, sp=sp, hot_tags=hot_tags, **detail)  # type: ignore


async def download_illust(request: Request, iid: int):
    """下载作品"""
    return await p_api.buy_illust(request.user, iid)


async def batch_download_illusts(request: Request):
    """批量下载作品"""
    form = await request.form()
    iids_str = form.get('iids', '')

    if not iids_str:
        return {'rc': 1, 'msg': '请选择要下载的图片'}

    try:
        # 解析图片ID列表
        iids = [int(iid.strip()) for iid in str(iids_str).split(',') if iid.strip().isdigit()]
    except (ValueError, AttributeError):
        return {'rc': 1, 'msg': '图片ID格式错误'}

    return await p_api.batch_download_illusts(request.user, iids)
