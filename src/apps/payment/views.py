import logging
import time
from urllib.parse import urlencode

from fastapi import Request
from fastapi.responses import HTMLResponse

import config
from apps.album.models import Album
from apps.payment.models import Order, PType, Status
from apps.payment.signature import rsa_sign, verify_signature
from config.payment.products import Coin, Vip
from libs.http import redirect, render

logger = logging.getLogger('info')


def products() -> HTMLResponse:
    """商品列表"""
    vips = [v for v in Vip.all() if not v.hidden]
    coins = Coin.all()
    return render('apps/purchase_products.html', pur_vips=vips, pur_coins=coins, n_vip=len(vips))


async def purchase_vip(request: Request, vid: int, ptype: PType):
    """购买 vip"""
    vip = Vip.get(vid)
    cur_vip = request.user.vip
    if cur_vip.level > vip.level:
        err = urlencode({'err': '您当前的 VIP 等级更高，无需购买'})
        return redirect(f'/w/purchase/?{err}')
    elif cur_vip.duration >= 36500:
        err = urlencode({'err': '您是尊贵的终身会员，无需续费'})
        return redirect(f'/w/purchase/?{err}')

    ua = request.headers.get('User-Agent', '')
    order, price = await Order.get_unpaid(request.user.id, vip, ptype, ua)
    payment_params = {
        'pid': config.PAY_PID,  # 商户ID
        'type': ptype.value,  # 支付方式
        'out_trade_no': f'{order.id:d}',  # 商户订单号
        'notify_url': config.PAY_NOTIFY_URL,  # 异步通知地址
        'return_url': config.PAY_RETURN_URL,  # 跳转通知地址
        'name': vip.name,  # 商品名称
        'money': f'{price:.2f}',  # 商品金额
        'timestamp': int(time.time()),  # 当前时间戳
        'sign_type': 'RSA',  # 签名类型
    }
    payment_params['sign'] = rsa_sign(payment_params)
    return render('apps/purchase_submit.html', payment_params=payment_params)


async def purchase_coin(request: Request, cid: int, ptype: PType):
    """购买 coin"""
    if request.user.vip.duration >= 36500:
        err = urlencode({'err': '您是尊贵的终身会员，无需充值次元币'})
        return redirect(f'/w/purchase/?{err}')

    coin = Coin.get(cid)
    ua = request.headers.get('User-Agent', '')
    order, price = await Order.get_unpaid(request.user.id, coin, ptype, ua)
    payment_params = {
        'pid': config.PAY_PID,  # 商户ID
        'type': ptype.value,  # 支付方式
        'out_trade_no': f'{order.id:d}',  # 商户订单号
        'notify_url': config.PAY_NOTIFY_URL,  # 异步通知地址
        'return_url': config.PAY_RETURN_URL,  # 跳转通知地址
        'name': coin.name,  # 商品名称
        'money': f'{price:.2f}',  # 商品金额
        'timestamp': int(time.time()),  # 当前时间戳
        'sign_type': 'RSA',  # 签名类型
    }
    payment_params['sign'] = rsa_sign(payment_params)
    return render('apps/purchase_submit.html', payment_params=payment_params)


async def purchase_album(request: Request, aid: int, ptype: PType):
    """购买 album"""
    album = await Album.get(id=aid)
    ua = request.headers.get('User-Agent', '')
    order, price = await Order.get_unpaid(request.user.id, album, ptype, ua)
    payment_params = {
        'pid': config.PAY_PID,  # 商户ID
        'type': ptype.value,  # 支付方式
        'out_trade_no': f'{order.id:d}',  # 商户订单号
        'notify_url': config.PAY_NOTIFY_URL,  # 异步通知地址
        'return_url': config.PAY_RETURN_URL,  # 跳转通知地址
        'name': album.title,  # 商品名称
        'money': f'{price:.2f}',  # 商品金额
        'timestamp': int(time.time()),  # 当前时间戳
        'sign_type': 'RSA',  # 签名类型
    }
    payment_params['sign'] = rsa_sign(payment_params)
    return render('apps/purchase_submit.html', payment_params=payment_params)


async def purchase_callback(
    pid: int,
    trade_no: str,
    out_trade_no: int,
    type: PType,  # noqa: A002, NOTE: 支付平台接口的字段名是 'type', 改成 'ptype' 会报错
    trade_status: str,
    name: str,
    money: str,
    sign: str,
    sign_type: str,
):
    """
    购买结果回调

    由支付平台发起，需返回success以表示服务器接收到了订单通知
    务必对返回的签名sign进行校验，并且判断 trade_status 的值是否等于TRADE_SUCCESS
    """
    payment_info = {
        'pid': pid,  # 商户ID
        'trade_no': trade_no,  # 平台订单号
        'out_trade_no': out_trade_no,  # 商户订单号
        'type': type.value,  # 支付方式
        'trade_status': trade_status,  # 交易状态
        'name': name,  # 商品名称
        'money': money,  # 商品金额
        'sign': sign,  # 签名字符串
        'sign_type': sign_type,  # 签名类型
    }
    if pid == config.PAY_PID and verify_signature(payment_info, sign_type, sign) and trade_status == 'TRADE_SUCCESS':
        order = await Order.get_or_none(id=out_trade_no)
        if order and await order.finish(type, Status.paid, float(money), payment_info):
            return HTMLResponse('success')
    logger.info(payment_info)
    return HTMLResponse('fail')


async def purchase_result(
    pid: int,
    trade_no: str,
    out_trade_no: int,
    type: PType,  # noqa: A002, NOTE: 支付平台接口的字段名是 'type', 改成 'ptype' 会报错
    trade_status: str,
    name: str,
    money: str,
    sign: str,
    sign_type: str,
):
    """购买结果"""
    payment_info = {
        'pid': pid,  # 商户ID
        'trade_no': trade_no,  # 平台订单号
        'out_trade_no': out_trade_no,  # 商户订单号
        'type': type.value,  # 支付方式
        'trade_status': trade_status,  # 交易状态
        'name': name,  # 商品名称
        'money': money,  # 商品金额
        'sign': sign,  # 签名字符串
        'sign_type': sign_type,  # 签名类型
    }
    if pid != config.PAY_PID:
        return render('apps/purchase_result.html', payment_info=payment_info, err='商户错误')
    if not verify_signature(payment_info, sign_type, sign):
        return render('apps/purchase_result.html', payment_info=payment_info, err='签名错误')
    if trade_status != 'TRADE_SUCCESS':
        return render('apps/purchase_result.html', payment_info=payment_info, err='交易状态错误')
    if order := await Order.get_or_none(id=out_trade_no):
        user = await order.user()
        product = await order.product()
        if await order.finish(type, Status.paid, float(money), payment_info):
            return render(
                'apps/purchase_result.html',
                order=order,
                user=user,
                product=product,
                payment_info=payment_info,
            )
        else:
            return render('apps/purchase_result.html', payment_info=payment_info, err='订单错误')
    else:
        return render('apps/purchase_result.html', payment_info=payment_info, err='订单不存在')
