import datetime
import re
from enum import IntEnum, StrEnum
from typing import Any, ClassVar, Self

from tortoise import fields
from tortoise.transactions import in_transaction

from apps.album.models import Album
from apps.user.models import User
from common.decorator import bg_task
from common.utils import get_device, iso_date
from config import TIME_ZONE, Coin, Vip
from libs.orm import Model

MOBILE_PATTERN = re.compile(r'mobile|android|iphone|ipad|ipod|blackberry|webos|windows phone')

__all__ = ['Goods', 'Order', 'PType', 'Status']

Product = Vip | Coin | Album


class Goods(StrEnum):
    """商品类型"""

    vip = 'vip'  # VIP
    coin = 'coin'  # 次元币
    album = 'album'  # 专辑


class Status(IntEnum):
    """订单状态"""

    unpaid = 0  # 未支付
    paid = 1  # 已支付
    refunded = 2  # 已退款
    frozen = 3  # 已冻结
    preauth = 4  # 预授权


class PType(StrEnum):
    """支付方式"""

    alipay = 'alipay'  # 支付宝
    wxpay = 'wxpay'  # 微信支付
    qqpay = 'qqpay'  # QQ钱包


class Order(Model):
    """订单"""

    uid = fields.IntField(unsigned=True, db_index=True, description='用户ID')
    gid = fields.IntField(unsigned=True, db_index=True, description='商品ID')
    goods = fields.CharEnumField(Goods, max_length=16, description='商品')
    amount = fields.FloatField(db_index=True, description='订单金额')
    status = fields.IntEnumField(Status, default=Status.unpaid, max_length=16, db_index=True, description='订单状态')
    ptype = fields.CharEnumField(PType, max_length=16, db_index=True, description='支付方式')
    device = fields.CharField(default='Unknown/Unknown', max_length=16, db_index=True, description='支付设备')
    created = fields.DatetimeField(auto_now_add=True, db_index=True, description='订单创建时间')
    finished = fields.DatetimeField(null=True, db_index=True, description='订单完成时间')
    extras: dict[str, Any] = fields.JSONField(default={}, description='额外信息')  # type: ignore

    class Meta:  # type: ignore
        ordering: ClassVar[list[str]] = ['id']
        expiration = 60 * 60  # 60 分钟过期

    def __str__(self):
        created = iso_date(self.created) or 'null'
        finished = iso_date(self.finished) or 'null'
        return (
            f'Order_{self.id:<3d}: User_{self.uid:<3d}  {self.goods.title():>5s}({self.gid})  '
            f'{self.amount:>3.0f} ¥  {self.status.name.title():>8s}  {self.ptype.name:>6s}  '
            f'{self.device:21s}  {created} fin: {finished}'
        )

    @property
    def expired(self):
        """订单是否过期"""
        deadline = self.created + datetime.timedelta(seconds=self.Meta.expiration)
        now = datetime.datetime.now(TIME_ZONE)
        return deadline <= now

    @classmethod
    async def get_unpaid(cls, uid: int, product: Product, ptype: PType, user_agent: str) -> tuple[Self, float]:
        """获取未支付的订单"""
        price = (product.price / 100) if isinstance(product, Album) else product.price
        goods = Goods(product.__class__.__name__.lower())
        device = get_device(user_agent)

        order, new = await cls.get_or_create(
            uid=uid,
            gid=product.id,
            goods=goods,
            amount=price,
            ptype=ptype,
            status=Status.unpaid,
            device=device,
        )

        # 添加定时任务，一小时(self.Meta.expiration) 后冻结订单
        if new:
            await order.auto_freeze()

        if not new and order.expired:
            async with in_transaction():
                await order.freeze()
                order = await cls.create(uid=uid, gid=product.id, goods=goods, amount=price, ptype=ptype)
        return order, price

    async def user(self):
        """获取订单所属用户"""
        if not hasattr(self, '_user'):
            self._user = await User.get(id=self.uid)
        return self._user

    async def product(self):
        """获取订单所属商品"""
        if not hasattr(self, '_product'):
            if self.goods == Goods.vip:
                self._product = Vip.get(self.gid)
            elif self.goods == Goods.coin:
                self._product = Coin.get(self.gid)
            elif self.goods == Goods.album:
                self._product = await Album.get(id=self.gid)
        return self._product

    async def finish(self, ptype: PType, status: Status, money: float, extras: dict[str, Any]):
        """完成订单"""
        if self.status == Status.paid:
            return True
        elif self.expired:
            await self.freeze()
            return False
        elif self.status == Status.unpaid and self.ptype == ptype and self.amount == money:
            now = datetime.datetime.now(TIME_ZONE)
            async with in_transaction():
                if status == Status.paid:
                    user = await self.user()
                    if self.goods == Goods.vip:
                        await user.set_vip(self.gid)
                    if self.goods == Goods.coin:
                        await user.topup(Coin.get(self.gid).amount)
                    if self.goods == Goods.album:
                        await user.buy_album(self.gid, free=True)

                self.status = status
                self.finished = now
                self.extras = extras
                await self.save()
            return True
        else:
            return False

    @classmethod
    async def freeze_all_expired(cls):
        """冻结全部过期订单"""
        now = datetime.datetime.now(TIME_ZONE)
        expire_time = now - datetime.timedelta(seconds=cls.Meta.expiration)
        expired_unpaid_orders = cls.filter(status=Status.unpaid, created__lte=expire_time)
        n_freezed = await expired_unpaid_orders.update(status=Status.frozen, finished=now)
        await cls.rebuild_cache()
        return n_freezed

    async def freeze(self):
        """如果订单过期则冻结"""
        now = datetime.datetime.now(TIME_ZONE)
        if self.status == Status.unpaid and self.expired:
            self.status = Status.frozen
            self.finished = now
            await self.save()

    @bg_task(Meta.expiration)
    async def auto_freeze(self):
        """自动冻结订单"""
        await self.freeze()
