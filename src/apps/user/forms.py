import re

from pydantic import BaseModel, EmailStr, field_validator

from config import LEN_VCODE, PWD_MIN_LEN

UPPER = re.compile(r'[A-Z]')
NUMBER = re.compile(r'[0-9]')


class LoginForm(BaseModel):
    email: EmailStr
    password: str


class RegisterForm(BaseModel):
    email: EmailStr
    vcode: str
    password: str  # 密码
    confirm: str  # 确认密码

    @field_validator('vcode')
    @classmethod
    def check_vcode_numberic(cls, vcode):
        if len(vcode) < LEN_VCODE:
            raise ValueError('验证码错误')
        return vcode

    @field_validator('password')
    @classmethod
    def check_password_strength(cls, password):
        if len(password) < PWD_MIN_LEN or not UPPER.search(password) or not NUMBER.search(password):
            raise ValueError(f'密码必须至少{PWD_MIN_LEN}个字符，并包含大写字母和数字')
        return password

    @field_validator('confirm')
    @classmethod
    def check_passwords_match(cls, confirm, values):
        if 'password' in values.data and values.data['password'] != confirm:
            raise ValueError('两次输入的密码不匹配')
        return confirm


class ProfileForm(BaseModel):
    password: str | None = None  # 新密码
    confirm: str | None = None  # 确认密码

    @field_validator('password')
    @classmethod
    def check_password_strength(cls, password):
        if password:
            if len(password) < PWD_MIN_LEN or not UPPER.search(password) or not NUMBER.search(password):
                raise ValueError(f'密码必须至少{PWD_MIN_LEN}个字符，并包含大写字母和数字')
        return password

    @field_validator('confirm')
    @classmethod
    def check_passwords_match(cls, confirm, values):
        if password := values.data.get('password'):
            if password and password != confirm:
                raise ValueError('两次输入的密码不匹配')
        return confirm
