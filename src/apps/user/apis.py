import datetime

from fastapi import BackgroundTasks

import common.errors as err
import config as cfg
from apps.user.models import User
from common.utils import random_code, safe_password, week_id
from libs.cache import redis
from libs.mail import send_verification_email


async def send_signup_verify_code(email: str, bg_tasks: BackgroundTasks) -> dict:
    """发送注册验证码"""
    if await User.filter(email=email).exists():  # 检查邮箱是否已被注册
        raise err.EmailExists

    # 发送验证邮件
    code = random_code(cfg.LEN_VCODE)
    await redis.set(f'VerifyCode::{email}', code, cfg.VCODE_AGE)  # 将验证码存入Redis
    bg_tasks.add_task(send_verification_email, email, code, cfg.VCODE_AGE)  # 发送验证码邮件

    return {'msg': '验证码已发送', 'rc': 0}


async def signup(email: str, password: str, verify_code: str):
    """注册新用户"""
    # 检查邮箱是否已被注册
    if await User.filter(email=email).exists():
        raise err.EmailExists

    # 检查验证码
    vcode_key = f'VerifyCode::{email}'
    if await redis.get(vcode_key) != verify_code:
        raise err.VcodeError

    # 创建新用户, 并登录
    name = email.split('@')[0].title()
    user = await User.create(email=email, password=safe_password(password), name=name)
    await redis.delete(vcode_key)  # 删除验证码

    return user


async def signin(email: str, password: str):
    """用户登录"""
    user = await User.get_or_none(email=email)
    if not user:
        raise err.EmailError
    elif user.banned:  # 检查用户是否被封禁
        raise err.AccountBanned
    elif not user.verify_password(password):  # 验证密码
        raise err.PasswordError
    else:
        return user


async def send_reset_verify_code(email: str, bg_tasks: BackgroundTasks) -> dict:
    """发送重置密码验证码"""
    if not await User.filter(email=email).exists():  # 检查邮箱是否存在
        raise err.EmailNotExists

    # 发送验证邮件
    code = random_code(cfg.LEN_VCODE * 2, pure_digital=False)
    await redis.set(f'VerifyCode::{email}', code, cfg.VCODE_AGE)  # 将验证码存入Redis
    bg_tasks.add_task(send_verification_email, email, code, cfg.VCODE_AGE)  # 发送验证码邮件

    return {'msg': '验证码已发送', 'rc': 0}


async def reset_password(email: str, new_password: str, verify_code: str):
    """重置密码"""
    user = await User.get_or_none(email=email)
    if not user:
        raise err.EmailError

    # 检查验证码
    vcode_key = f'VerifyCode::{email}'
    if await redis.get(vcode_key) != verify_code:
        raise err.VcodeError

    # 更新密码
    await user.set_password(new_password)
    await redis.delete(vcode_key)  # 删除验证码

    return {'msg': '密码重置成功', 'rc': 0}


async def modify_profile(user: User, password: str | None):
    """修改个人资料"""
    if not password:
        return {'msg': '密码为空！', 'rc': 1}

    hashed_pwd = safe_password(password)
    if user.password != hashed_pwd:
        user.password = hashed_pwd
        await user.save(update_fields=['password'])
        return {'msg': '密码修改成功！', 'rc': 0}
    else:
        return {'msg': '密码没有变化！', 'rc': 0}


async def checkin_status(user: User):
    """获取打卡状态"""
    week_key = f'CheckInWeek::{week_id()}'
    checkin_log: list[int] = await redis.hget(week_key, user.id, [])  # type: ignore
    return checkin_log


async def checkin(user: User):
    """打卡领次元币

    七天的值分别为: 60, 100, 60, 100, 60, 200, 600。
    一周最多可得 1180 枚次元币
    """
    week_key = f'CheckInWeek::{week_id()}'
    weekday = datetime.datetime.now().weekday()
    checkin_log: list[int] = await redis.hget(week_key, user.id, [])  # type: ignore

    if weekday in checkin_log:
        return {'rc': 0, 'msg': f'今天已经成功打卡！<br>本周累计打卡 {len(checkin_log)} 天。'}
    else:
        # 计算打卡奖励
        checkin_log.append(weekday)
        n_chk_days = len(checkin_log)
        mul = 2 if user.has_perms('dbl_chkin') else 1  # 打卡奖励倍率
        coins = list(cfg.CHECKIN_AWARD.values())[n_chk_days - 1] * mul

        # 打卡
        await user.topup(coins)
        await redis.hset(week_key, str(user.id), checkin_log)
        await redis.expire(week_key, 86400 * 8)  # 8天过期

        return {'rc': 0, 'msg': f'本周打卡第 {n_chk_days} 天。<br>收获 {coins} 枚次元币！'}
