import smtplib
from email.header import Header
from email.mime.text import MIMEText
from email.utils import formataddr

from mailjet_rest import Client

from config import EMAIL

mailjet = Client(auth=(EMAIL['API_KEY'], EMAIL['API_SECRET']), version='v3.1')


def send_verification_email(email: str, code: str, expiration: int):
    """发送验证邮件

    Args:
        email: 收件人邮箱
        code: 验证码
        expiration: 有效时间（秒）
    """
    data = {
        'Messages': [
            {
                'From': {'Email': EMAIL['SENDER_MAIL'], 'Name': EMAIL['SENDER_NAME']},
                'To': [{'Email': email, 'Name': email.split('@')[0]}],
                'Subject': f'【{EMAIL["SENDER_NAME"]}】验证邮件',
                'TextPart': f'您的验证码是：{code}。请勿泄露给他人，验证码有效期 {expiration // 60} 分钟。',
                'HTMLPart': f'<h3>您的验证码是：{code}</h3>请勿泄露给他人，验证码有效期 {expiration // 60} 分钟。',
            }
        ]
    }

    return mailjet.send.create(data=data)


def send_email_by_smtp(to_addr: str, subject: str, content: str):
    """发送邮件

    Args:
        to_addr: 收件人邮箱
        subject: 邮件主题
        content: 邮件内容

    Returns:
        bool: 发送是否成功
    """
    msg = MIMEText(content, 'plain', 'utf-8')
    sender = EMAIL['sender_name']
    msg['From'] = Header(formataddr(sender) if isinstance(sender, tuple) else sender)  # type: ignore
    msg['To'] = Header(to_addr)  # type: ignore
    msg['Subject'] = Header(subject)  # type: ignore

    try:
        with smtplib.SMTP_SSL(host=EMAIL['HOST'], port=EMAIL['PORT']) as smtp:  # type: ignore
            smtp.login(EMAIL['API_KEY'], EMAIL['API_SECRET'])  # type: ignore
            return smtp.sendmail(EMAIL['SENDER_MAIL'], to_addr, msg.as_string())  # type: ignore
    except Exception:
        return False
