import datetime
import j<PERSON>
from contextvars import ContextVar
from enum import Enum
from functools import wraps
from typing import Any
from urllib import parse

from fastapi import HTTPException, Response
from fastapi.requests import Request
from fastapi.responses import HTMLResponse, RedirectResponse
from fastapi.responses import JSONResponse as _JSONResponse
from jinja2 import Environment, FileSystemLoader

import config
from common.utils import cn_date
from libs.static import static

# 遍历配置中的模板路径，初始化对应的 Jinja2 环境
templates = {}
for key, path in config.TEMPLATES.items():
    templates[key] = Environment(loader=FileSystemLoader(path), autoescape=True)
    templates[key].globals['static'] = static
    templates[key].globals['sum'] = sum
    templates[key].globals['round'] = round
    templates[key].globals['cn_date'] = cn_date
    templates[key].filters['nl2br'] = lambda s: s.replace('\n', '<br />')


_State: ContextVar[dict[str, Any]] = ContextVar('State', default={})  # noqa: B039


class State:
    """状态"""

    @staticmethod
    def init():
        """初始化状态"""
        return _State.set({})

    @staticmethod
    def set(**kwargs):
        """设置状态"""
        st = _State.get()
        st.update(kwargs)

    @staticmethod
    def get(name: str, default=None) -> Any:
        """获取状态"""
        st = _State.get()
        return st.get(name, default)

    @staticmethod
    def get_all() -> dict:
        """获取所有状态"""
        return _State.get()

    @staticmethod
    def reset(token):
        """清空状态"""
        _State.reset(token)


class JSONEncoder(json.JSONEncoder):
    """自定义 JSON 编码器"""

    from libs.orm import Model  # 防止循环引用

    def default(self, o):
        if isinstance(o, datetime.datetime):
            return cn_date(o, 'm')
        elif isinstance(o, datetime.date):
            return cn_date(o, 'd')
        elif isinstance(o, Enum):
            return o.value
        elif isinstance(o, Exception):
            rc = getattr(o, 'rc', 500)
            msg = getattr(o, 'msg', f'{o.__class__.__name__}' if config.DEBUG else 'Internal Server Error')
            return {'rc': rc, 'msg': msg}
        elif isinstance(o, self.Model):
            return o.to_dict()
        return super().default(o)


class JSONResponse(_JSONResponse):
    def render(self, content: Any) -> bytes:
        return json.dumps(
            content,
            cls=JSONEncoder,
            ensure_ascii=False,
            indent=None,
            sort_keys=True,
            separators=(',', ':'),
        ).encode('utf-8')


def render(template_name: str, *, load_env=True, status_code: int = 200, **kwargs: Any) -> HTMLResponse:
    """
    渲染指定的模板并返回 HTML 响应

    Args:
        template_name (str): 模板名称，格式为 "模板文件名"
        **kwargs: 传递给模板的变量参数

    Returns:
        HTMLResponse: 渲染后的 HTML 响应对象
    """
    if load_env:
        request = State.get('request')
        kwargs['request'] = request

        # 插入 user
        if 'user' not in kwargs:
            kwargs['user'] = request.user

        # 插入 max_sanity
        if 'max_sanity' not in kwargs:
            kwargs['max_sanity'] = State.get('max_sanity')

        # 插入 page
        if 'page' not in kwargs:
            kwargs['page'] = request.query_params.get('page', 1)

        # 插入 config
        if 'config' not in kwargs:
            kwargs['cfg'] = config

    # 获取模板并渲染
    tmp_key, template_name = template_name.split('/')
    template = templates[tmp_key].get_template(template_name)
    html = template.render(**kwargs)
    return HTMLResponse(html, status_code)


def jsonify(view_func):
    """确保返回 JSON 格式"""
    default = {'rc': 0}

    @wraps(view_func)
    async def wrapper(*args, **kwargs):
        result = await view_func(*args, **kwargs)
        if isinstance(result, Response):
            return result

        if not isinstance(result, dict):
            result = {'data': result}

        content = default.copy()
        content.update(result)

        return JSONResponse(content)

    return wrapper


def redirect(url: str, status_code: int = 302) -> RedirectResponse:
    """重定向"""
    return RedirectResponse(url, status_code)


def pagination(request: Request) -> dict[str, Any]:
    """生成分页链接"""
    query_params = parse.parse_qs(request.url.query)
    page = int(query_params.get('page', [1])[0])
    page = max(1, page)
    # 上一页
    query_params['page'] = [str(page - 1 if page > 2 else 1)]
    prev_url = request.url.path + '?' + parse.urlencode(query_params, doseq=True)
    # 下一页
    query_params['page'] = [str(page + 1)]
    next_url = request.url.path + '?' + parse.urlencode(query_params, doseq=True)
    return {'prev': prev_url, 'page': page, 'next': next_url}


def abort(status_code: int):
    """终止请求"""
    raise HTTPException(status_code=status_code)
