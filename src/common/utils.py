import asyncio
import datetime
import json
import os
import random
import re
import string
import time
from collections.abc import Callable, Generator, Sequence
from functools import wraps
from hashlib import pbkdf2_hmac
from itertools import islice
from pathlib import Path
from typing import Any, Literal, TypeVar

import py7zr
from PIL import Image, ImageFilter
from user_agents import parse as ua_parse
from xxhash import xxh32
from zhconv import convert, issimp

from config import SECRET_KEY, TIME_ZONE
from libs.terminal import cyan, print_err, print_inf, print_warn, yellow

Caller = TypeVar('Caller', bound=Callable[..., Any])

# 敏感词
SENSITIVE_WORDS = re.compile(
    r'乳|奶|胸罩|奸|姦|尻|肛|淫|尿|孕|内裤|裸体|全裸|露出|走光|'
    r'阴茎|阴毛|阴垢|精液|乳交|手交|足交|中出|自慰|潮吹|'
    r'子宫|卵巢|内裤|紧缚|捆绑|后庭|骆驼趾|R[-_ ]?18',
    re.IGNORECASE,
)


# 日文字符
JP_CHR = (
    {chr(i) for i in range(0x3041, 0x3097)}
    | {chr(i) for i in range(0x309D, 0x30FB)}
    | {chr(i) for i in range(0x30FC, 0x30FF)}
    | {chr(i) for i in range(0x31F0, 0x31FF)}
)

# 正则表达式
NUMERIC = re.compile(r'^\d+')
ARTIST_NAME_SLASH = re.compile(r'([\(（\[【@＠/／\-◆◇■＊◎]|FANBOX|新刊|[金木水火土日月]曜|お仕事|C\d+).*')
F_IMAGE = re.compile(r'\.(?:jpg|jpeg|png|gif|webp|bmp|tiff|tif|heic|heif|avif)$', re.IGNORECASE)
F_ILLUST = re.compile(r'\d+_p\d+\.(?:jpg|jpeg|png|gif)$', re.IGNORECASE)
F_VIDEO = re.compile(r'\.(?:mp4|mov|avi|mkv|webm|flv|f4v|wmv|rmvb|3gp|m4v)$', re.IGNORECASE)
F_JSON = re.compile(r'\.json$', re.IGNORECASE)

# 字节单位
KB = 1024
MB = 1024 * KB
GB = 1024 * MB
TB = 1024 * GB

HASH_MAP = '0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz'


def is_sensitive(word: str) -> bool:
    """是否是敏感词"""
    return bool(SENSITIVE_WORDS.search(word))


def jp_chars(word: str) -> set[str]:
    """单词中的日文字符"""
    return JP_CHR & set(word)


def is_chars(word: str) -> bool:
    """判断是否是普通字符"""
    return all(ord(c) < 0x2E81 for c in word)  # 从 0x2e81 出现中、日、韩等文字


def get_lang(word: str) -> Literal['en', 'jp', 'hans', 'hant']:
    """获取字符语言"""
    if is_chars(word):
        return 'en'
    elif jp_chars(word):
        return 'jp'
    elif issimp(word):
        return 'hans'
    else:
        return 'hant'


def replace_cn_punctuation(text):
    """替换中文标点为英文标点"""
    punctuation_map = {
        '，': ',',
        '。': '.',
        '？': '?',
        '！': '!',
        '；': ';',
        '：': ':',
        '“': '"',
        '”': '"',
        '（': '(',
        '）': ')',
    }

    pattern = '[' + re.escape(''.join(punctuation_map.keys())) + ']'
    text = re.sub(pattern, lambda m: punctuation_map[m.group()], text)
    return text


def clean_name(name: str) -> str:
    """清洗名字"""
    return ARTIST_NAME_SLASH.sub('', replace_cn_punctuation(name)).strip().replace(' ', '_')


def readable_tag(name: str, trans: str) -> str:
    """可读性更好的 tag"""
    if not trans:
        wd = name
    else:
        n_lang = get_lang(name)
        t_lang = get_lang(trans)

        if n_lang == 'jp':
            wd = trans
        elif t_lang == 'en':
            wd = name
        else:
            wd = trans
    return convert(wd, 'zh-hans')


def readable_size(n_byte: int) -> str:
    """将字节大小转换为人类可读的字符串"""
    if n_byte < KB:
        return f'{n_byte}B'
    elif n_byte < MB:
        return f'{n_byte / KB:.1f}KB'
    elif n_byte < GB:
        return f'{n_byte / MB:.1f}MB'
    elif n_byte < TB:
        return f'{n_byte / GB:.1f}GB'
    else:
        return f'{n_byte / TB:.1f}TB'


def get_device(user_agent: str):
    """根据 user agent 获取设备类型"""
    ua = ua_parse(user_agent)
    dev = ua.device.model or 'Unknown'
    os = ua.os.family or 'Unknown'
    ver = ua.os.version_string or ''
    return f'{dev}/{os}_{ver}'.replace(' ', '')


def random_code(length: int = 6, pure_digital: bool = True) -> str:
    """生成随机码, 相邻数字不重复且不连续, 且不使用超过2次的数字"""
    codes: list[str] = []
    pool = (string.digits if pure_digital else string.digits + string.ascii_letters) * 2

    if length < 2:
        raise ValueError('长度不能小于 2')
    if len(pool) < length:
        raise ValueError(f'数字池长度 {len(pool)} 小于所需长度 {length}')

    sorted_codes: list[str] = []
    while codes == sorted_codes:
        codes = random.sample(pool, length)
        sorted_codes = sorted(codes)

    return ''.join(map(str, codes))


def safe_password(password):
    """创建安全的 password"""
    password = password.encode('utf8')
    return pbkdf2_hmac('sha256', password, SECRET_KEY, 100000).hex()


def ibatch(iterable, batch_size):
    """分批迭代器"""
    it = iter(iterable)
    while batch := list(islice(it, batch_size)):
        yield batch


def date_id(tm: datetime.datetime | float | None = None):
    """日期 ID"""
    if tm is None:
        tm = time.time()
    elif isinstance(tm, datetime.datetime):
        tm = tm.timestamp()
    return int((tm - time.timezone) // 86400)


def week_id(tm: datetime.datetime | float | None = None):
    """星期的 ID"""
    if tm is None:
        tm = time.time()
    elif isinstance(tm, datetime.datetime):
        tm = tm.timestamp()
    return int((tm - time.timezone + 86400 * 3) // 604800)


def cn_date(date: datetime.datetime | datetime.date, accuracy='m', ast_tz=True) -> str:
    """中文日期格式化"""
    if not date:
        return ''  # 处理空值
    if type(date) is datetime.date:
        date = datetime.datetime.combine(date, datetime.datetime.min.time())  # 转换为 datetime 对象
    if ast_tz and isinstance(date, datetime.datetime):
        date = date.astimezone(TIME_ZONE)  # 转换为本地时区

    if accuracy == 'd':
        return date.strftime('%Y年%m月%d日')
    elif accuracy == 'h':
        return date.strftime('%Y年%m月%d日 %H时')
    elif accuracy == 'm':
        return date.strftime('%Y年%m月%d日 %H时%M分')
    else:
        return date.strftime('%Y年%m月%d日 %H时%M分%S秒')


def iso_date(date: datetime.datetime | datetime.date, accuracy='m', ast_tz=True) -> str:
    """ISO 日期格式化"""
    if not date:
        return ''  # 处理空值
    if type(date) is datetime.date:
        date = datetime.datetime.combine(date, datetime.datetime.min.time())  # 转换为 datetime 对象
    if ast_tz and isinstance(date, datetime.datetime):
        date = date.astimezone(TIME_ZONE)  # 转换为本地时区

    if accuracy == 'd':
        return date.strftime('%Y-%m-%d')
    elif accuracy == 'h':
        return date.strftime('%Y-%m-%d %H')
    elif accuracy == 'm':
        return date.strftime('%Y-%m-%d %H:%M')
    else:
        return date.strftime('%Y-%m-%d %H:%M:%S')


def split_num_and_chars(string: str) -> tuple[int, str]:
    """将字符串按开头的数字进行截断"""
    if matched := NUMERIC.match(string):
        return int(matched.group()), string[matched.end() :]
    else:
        return 0, string


def is_image(filename: str) -> bool:
    """是否是图片文件"""
    return bool(F_IMAGE.search(filename))


def is_illust(filename: str) -> bool:
    """是否是 P 站插画文件"""
    return bool(F_ILLUST.match(filename))


def walk_files(path: Path) -> Generator[Path, None, None]:
    """迭代遍历目录中的所有非隐藏文件"""
    for root, dirs, files in os.walk(path):
        dpath = Path(root)
        dirs.sort()
        files.sort(key=lambda fname: split_num_and_chars(fname))
        for fname in files:
            fpath = dpath / fname
            if (fpath.is_file() or path.is_symlink()) and fpath.name[0] != '.':
                yield fpath


def find_all_files(paths: str | Path | Sequence[str | Path], recursively=True) -> Generator[Path, None, None]:
    """查找目录中全部的文件"""
    if isinstance(paths, (str, Path)):
        paths = [paths]

    for path in (p if isinstance(p, Path) else Path(p) for p in paths):
        if path.is_dir():
            if recursively:
                yield from walk_files(path)
            else:
                items = sorted(path.iterdir(), key=lambda p: split_num_and_chars(p.stem))
                for fpath in items:
                    if (fpath.is_file() or path.is_symlink()) and fpath.name[0] != '.':
                        yield fpath
        elif path.is_file() or path.is_symlink():
            yield path


def find_all_images(image_paths, recursively=True) -> Generator[Path, None, None]:
    """查找目录中全部的图片文件"""
    for path in find_all_files(image_paths, recursively):
        if F_IMAGE.search(path.name):
            yield path


def find_illusts(image_paths, recursively=True) -> Generator[Path, None, None]:
    """查找P站插画"""
    for path in find_all_files(image_paths, recursively):
        if F_ILLUST.match(path.name):
            yield path


def find_json_files(json_paths, recursively=True) -> Generator[Path, None, None]:
    """查找 JSON 文件"""
    for path in find_all_files(json_paths, recursively):
        if F_JSON.search(path.name):
            yield path


def find_illust_and_json(src_dirs: list[Path], json_dir: Path) -> Generator[tuple[Path, dict], None, None]:
    """迭代查找插画和对应的 JSON 文件"""
    # 从指定目录中获取所有插画
    for ipath in find_illusts(src_dirs):
        # 根据插画 ID 找到对应的 JSON 文件
        iid = int(ipath.stem.split('_p')[0])
        j_path = json_dir / f'{iid}.json'
        if not j_path.exists():
            # print_warn(f'not found: {j_path}')
            continue
        # 读取 JSON 文件，并返回插画路径和 JSON 数据
        with j_path.open('r') as fp:
            ijson = json.load(fp)
        yield ipath, ijson


def find_image_dirs(search_dir: Path) -> Generator[tuple[Path, list[str]], None, None]:
    """迭代查找仅包含图片的目录"""
    for root, dirs, files in os.walk(search_dir):
        root_path = Path(root)
        if root_path.name.startswith('.'):
            dirs.clear()  # 不再遍历隐藏目录下的子目录
            continue
        # 如果包含非隐藏的子目录，直接继续
        if any(not d.startswith('.') for d in dirs):
            continue

        dirs.sort()
        files.sort()
        # 过滤隐藏文件并检查是否全部为图片
        valid_files = [f for f in files if not f.startswith('.')]
        image_files = [f for f in valid_files if is_image(f)]

        # 仅包含图片的目录
        if image_files and image_files == valid_files:
            yield root_path.absolute(), image_files


def _rename_item(
    parent: str,
    filename: str,
    pattern: str | re.Pattern,
    repl: str | Callable,
    dry_run: bool = False,
) -> int:
    """重命名文件或目录"""
    if isinstance(pattern, str) and isinstance(repl, str) and pattern in filename:
        new_name = filename.replace(pattern, repl)
    elif isinstance(pattern, re.Pattern) and pattern.search(filename):
        new_name = pattern.sub(repl, filename)
    else:
        return 0

    old_path = os.path.join(parent, filename)
    new_path = os.path.join(parent, new_name)

    if os.path.exists(new_path):
        print_warn(f'Item exists: {new_path}')
        return 0

    try:
        if dry_run:
            print(f'Rename: {cyan(old_path, True)} -> {yellow(new_path, True)}')
        if not dry_run:
            os.rename(old_path, new_path)
        return 1
    except OSError as e:
        print_err(f"Rename '{old_path}' failed: {e}")
        return 0


def batch_rename(
    directory: str,
    pattern: str | re.Pattern,
    repl: str | Callable,
    ignore_hidden=True,
    dry_run=False,
) -> int:
    """递归处理指定目录，将子目录或子文件名中的某个字符替换成指定字符"""
    if not os.path.isdir(directory):
        return 0

    rename_count = 0

    # 深度优先进行遍历，这样可以安全地在同一循环中重命名目录
    for root, dirs, files in os.walk(directory, topdown=False):
        if ignore_hidden and os.path.basename(os.path.abspath(root)).startswith('.'):
            continue  # 跳过整个隐藏目录

        # 处理文件和目录
        for item in files + dirs:
            if ignore_hidden and item.startswith('.'):
                continue
            rename_count += _rename_item(root, item, pattern, repl, dry_run)

    return rename_count


def has_transparency(img: Image.Image) -> bool:
    """判断图片是否包含透明像素点"""
    if img.mode != 'RGBA':
        return False
    return 0 in img.resize((64, 64)).getchannel('A').getdata()  # type: ignore


def crop_image(image: Path | str | Image.Image, max_aspect: float):
    """
    裁剪图片, 使得图片的长边对短边的比值不超过 max_aspect

    :param image: 图片路径或 PIL.Image 对象
    :param max_aspect: 裁剪后的图片长边对短边最大比值。max_aspect >= 1，如果小于 1 则自动取倒数，如果为 0 则直接返回

    裁剪规则:
    1. 横版图, 宽高比大于 max_aspect, 则裁剪宽度, 高度不变
    2. 横版图, 宽高比小于等于 max_aspect, 则不做裁剪
    3. 竖版图, 宽高比大于等于 1 / max_aspect, 则不做裁剪
    4. 竖版图, 宽高比小于 1 / max_aspect, 则裁剪高度, 宽度不变
    """
    if not isinstance(image, Image.Image):
        image = Image.open(image)

    if not max_aspect:
        return image

    width, height = image.size
    origin_aspect = width / height
    h_aspect, v_aspect = (max_aspect, 1 / max_aspect) if max_aspect > 1 else (1 / max_aspect, max_aspect)

    if width > height:
        if origin_aspect > h_aspect:
            cropped_height = height
            cropped_width = int(height * h_aspect)  # 裁剪后的宽度
            top = 0
            left = round((width - cropped_width) / 2)
        else:
            return image
    else:
        if origin_aspect < v_aspect:
            cropped_width = width
            cropped_height = int(width / v_aspect)  # 裁剪后的高度
            top = round((height - cropped_height) / 2)
            left = 0
        else:
            return image

    return image.crop((left, top, left + cropped_width, top + cropped_height))


def make_thumbnail(
    img_path: Path | str,
    thumb_dir: Path | str,
    size: int,
    *,
    max_aspect: float = 0.0,
    overwrite: bool = False,
    ext: str = 'webp',
    quality: int = 90,
    prefix: str = '',
    suffix: str = '',
    strict: bool = False,
) -> Path:
    """创建缩略图"""
    img_path, thumb_dir = Path(img_path), Path(thumb_dir)

    # 创建缩略图目录
    if not thumb_dir.exists():
        thumb_dir.mkdir(mode=0o755, parents=True, exist_ok=True)
    # 缩略图路径
    thumbnail_path = thumb_dir / f'{prefix}{img_path.stem}{suffix}.{ext}'
    if not overwrite and thumbnail_path.exists():
        return thumbnail_path

    # 处理图片
    with Image.open(img_path) as img:
        if max_aspect:
            img = crop_image(img, max_aspect)
        if strict or max(img.size) > size:
            img.thumbnail((size, size), reducing_gap=2.0)
        if img.mode == 'RGBA' and not has_transparency(img):
            img = img.convert('RGB')
        sharpened = img.filter(ImageFilter.DETAIL)
        sharpened.save(thumbnail_path, quality=quality, optimize=True)
        return thumbnail_path


def resize_and_crop(img: Image.Image | Path | str, target_width: int, target_height: int) -> Image.Image:
    """
    等比例缩放并裁剪图片，使其填充指定尺寸的区域
    - 宽图：缩放至适合高度，裁剪左右（保留中间）
    - 高图：缩放至适合宽度，裁剪上下（保留中间）

    Args:
        img: PIL图片对象
        target_width: 目标宽度
        target_height: 目标高度

    Returns:
        处理后的PIL图片对象
    """
    if isinstance(img, (Path, str)):
        img = Image.open(img)

    img_ratio = img.width / img.height
    target_ratio = target_width / target_height

    if img_ratio > target_ratio:  # 宽图
        new_width = int(target_height * img_ratio)  # 缩放至适合高度，并裁剪左右多余部分（保留中间区域）
        img.thumbnail((new_width, target_height), reducing_gap=2.0)
        # 裁剪超出的宽度部分，保留中间区域
        x_offset = (new_width - target_width) // 2
        img_final = img.crop((x_offset, 0, x_offset + target_width, target_height))
    else:  # 高图
        new_height = int(target_width / img_ratio)  # 缩放至适合宽度，并裁剪上下多余部分（保留中间区域）
        img.thumbnail((target_width, new_height), reducing_gap=2.0)
        # 裁剪超出的高度部分，保留中间区域
        y_offset = (new_height - target_height) // 2
        img_final = img.crop((0, y_offset, target_width, y_offset + target_height))

    return img_final


def merge_images(
    left_img: Image.Image | Path | str,
    right_img: Image.Image | Path | str,
    output_dir: Path,
    *,
    prefix: str = '',
    fmt='webp',
    new_width=1200,
    aspect=1.414,
    quality=90,
) -> Path:
    """
    将两张图片左右合并成一张新图，满足以下条件：
    - 两张图在新图中左右排列，各占新图面积的一半
    - 新图宽高比为1.414
    - 新图宽度为1200像素
    """
    new_height = int(new_width / aspect)
    half_width = new_width // 2

    # 等比例缩放并裁剪每张图片
    left_img = resize_and_crop(left_img, half_width, new_height)
    right_img = resize_and_crop(right_img, half_width, new_height)

    # 创建一个新的空白图片, 并将处理后的图片粘贴到新图片的相应位置
    new_img = Image.new('RGB', (new_width, new_height))
    new_img.paste(left_img, (0, 0))
    new_img.paste(right_img, (half_width, 0))

    # 保存合并后的图片
    new_img = new_img.filter(ImageFilter.DETAIL)
    output_path = Path(output_dir / f'{prefix}{str_hash(new_img.tobytes("jpeg", "RGB"), True)}.{fmt}')
    new_img.save(output_path, quality=quality, optimize=True)
    return output_path


def merge_three_images(
    left_img: Image.Image | Path | str,
    tr_img: Image.Image | Path | str,
    br_img: Image.Image | Path | str,
    output_dir: Path,
    *,
    prefix: str = '',
    fmt='webp',
    new_width=1200,
    aspect=1.414,
    quality=90,
) -> Path:
    """
    将三张图片合并成一张新图，布局为:
    - 第一张图占据左边一半区域
    - 另外两张图在右侧上下排列，均分右侧区域
    - 新图宽度为1200像素，宽高比为1.414
    """
    new_height = int(new_width / aspect)
    # 计算各区域尺寸
    left_width = new_width // 2
    right_width = new_width - left_width
    right_height = new_height // 2

    # 等比例缩放和裁剪三张图片
    left_img = resize_and_crop(left_img, left_width, new_height)  # 左侧
    tr_img = resize_and_crop(tr_img, right_width, right_height)  # 右上
    br_img = resize_and_crop(br_img, right_width, right_height)  # 右下

    # 创建一个新的空白图片, 并将处理后的图片粘贴到新图片的相应位置
    new_img = Image.new('RGB', (new_width, new_height))
    new_img.paste(left_img, (0, 0))  # 左侧
    new_img.paste(tr_img, (left_width, 0))  # 右上
    new_img.paste(br_img, (left_width, right_height))  # 右下

    # 保存合并后的图片
    new_img = new_img.filter(ImageFilter.DETAIL)
    output_path = Path(output_dir / f'{prefix}{str_hash(new_img.tobytes("jpeg", "RGB"), True)}.{fmt}')
    new_img.save(output_path, quality=quality, optimize=True)
    return output_path


def timer(func: Caller) -> Caller:
    """打印函数执行时间的装饰器"""
    if asyncio.iscoroutinefunction(func):

        @wraps(func)
        async def async_wrapper(*args: Any, **kwargs: Any) -> Any:
            start = time.time()
            try:
                result = await func(*args, **kwargs)
            finally:
                end = time.time()
                print_inf(f'{func.__name__} 执行时间: {end - start:.4f} 秒')
            return result

        return async_wrapper  # type: ignore

    else:

        @wraps(func)
        def sync_wrapper(*args: Any, **kwargs: Any) -> Any:
            start = time.time()
            try:
                result = func(*args, **kwargs)
            finally:
                end = time.time()
                print_inf(f'{func.__name__} 执行时间: {end - start:.4f} 秒')
            return result

        return sync_wrapper  # type: ignore


def compress_files(zip_path: Path | str, *item_paths: Path | str, password: str | None = None):
    """使用 py7zr 压缩文件或目录

    Args:
        zip_path: 生成的压缩包路径
        *item_paths: 要压缩的文件或目录路径
        password: 可选的密码保护，默认为 None
        compression: 压缩方式，默认为 ZIP_STORED (不压缩)
    """
    zip_path = Path(zip_path) if isinstance(zip_path, str) else zip_path

    # 确保目标目录存在
    if not zip_path.parent.exists():
        zip_path.parent.mkdir(parents=True, exist_ok=True)

    # 创建压缩文件
    filters = [{'id': py7zr.FILTER_COPY}]
    with py7zr.SevenZipFile(zip_path, 'w', filters=filters, dereference=True, password=password) as zip_file:
        zip_file.set_encrypted_header(bool(password))

        # 处理每个文件路径
        for path in item_paths:
            path = (Path(path) if isinstance(path, str) else path).absolute()
            if path.is_dir():
                for f_path in walk_files(path):
                    zip_file.write(f_path, arcname=f_path.relative_to(path.parent).as_posix())
            elif path.is_file():
                zip_file.write(path, arcname=path.name)
            else:
                print_warn(f'not regular file: {path.absolute()}')
        else:
            if not zip_file.files:
                zip_path.unlink()

    return zip_file


def b62encode(n: int) -> str:
    """将整数转换为 62 进制字符串"""
    encoded = ''
    while n:
        n, remainder = divmod(n, 62)
        encoded = HASH_MAP[remainder] + encoded
    return encoded


def b62decode(s: str) -> int:
    """将 62 进制字符串转换为整数"""
    n = 0
    for c in s:
        n = n * 62 + HASH_MAP.index(c)
    return n


def str_hash(string: str | bytes, enb62: bool = False) -> str:
    """字符串哈希"""
    if isinstance(string, str):
        string = string.encode('utf-8')
    xx_hash = xxh32(string)
    if enb62:
        return b62encode(xx_hash.intdigest())
    else:
        return xx_hash.hexdigest().upper()


def file_hash(path: Path | str, enb62: bool = False) -> str:
    """文件哈希"""
    path = Path(path) if isinstance(path, str) else path
    return str_hash(path.read_bytes(), enb62)
